<?php
/**
 * اختبار API رفع الملفات
 * Upload API Test
 */

echo "<h2>اختبار API رفع الملفات</h2>";
echo "<style>
    .success { color: #059669; font-weight: bold; }
    .error { color: #dc2626; font-weight: bold; }
    .warning { color: #d97706; font-weight: bold; }
    .info { color: #2563eb; }
    pre { background: #f8fafc; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .test-section { margin: 20px 0; padding: 15px; border: 1px solid #e2e8f0; border-radius: 8px; }
</style>";

echo "<div class='test-section'>";
echo "<h3>1. اختبار تحميل ملفات النظام</h3>";
echo "<pre>";

// اختبار تحميل config.php
echo "اختبار تحميل config/config.php...\n";
try {
    require_once 'config/config.php';
    echo "<span class='success'>✅ تم تحميل config.php بنجاح</span>\n";
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في تحميل config.php: " . $e->getMessage() . "</span>\n";
}

// اختبار تحميل CSVImporter
echo "\nاختبار تحميل includes/CSVImporter.php...\n";
try {
    require_once 'includes/CSVImporter.php';
    echo "<span class='success'>✅ تم تحميل CSVImporter.php بنجاح</span>\n";
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في تحميل CSVImporter.php: " . $e->getMessage() . "</span>\n";
}

echo "</pre></div>";

echo "<div class='test-section'>";
echo "<h3>2. اختبار الاتصال بقاعدة البيانات</h3>";
echo "<pre>";

try {
    $db = getDB();
    echo "<span class='success'>✅ تم الاتصال بقاعدة البيانات بنجاح</span>\n";
    
    // اختبار الجداول المطلوبة
    $requiredTables = ['imported_files', 'system_settings'];
    foreach ($requiredTables as $table) {
        if ($db->tableExists($table)) {
            echo "<span class='success'>✅ جدول $table موجود</span>\n";
        } else {
            echo "<span class='error'>❌ جدول $table مفقود</span>\n";
        }
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</span>\n";
}

echo "</pre></div>";

echo "<div class='test-section'>";
echo "<h3>3. اختبار إنشاء CSVImporter</h3>";
echo "<pre>";

try {
    $importer = new CSVImporter();
    echo "<span class='success'>✅ تم إنشاء CSVImporter بنجاح</span>\n";
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في إنشاء CSVImporter: " . $e->getMessage() . "</span>\n";
}

echo "</pre></div>";

echo "<div class='test-section'>";
echo "<h3>4. اختبار دوال JSON</h3>";
echo "<pre>";

// اختبار دالة sendJsonResponse
echo "اختبار دوال JSON...\n";
if (function_exists('sendJsonResponse')) {
    echo "<span class='success'>✅ دالة sendJsonResponse موجودة</span>\n";
} else {
    echo "<span class='error'>❌ دالة sendJsonResponse مفقودة</span>\n";
}

if (function_exists('sendErrorResponse')) {
    echo "<span class='success'>✅ دالة sendErrorResponse موجودة</span>\n";
} else {
    echo "<span class='error'>❌ دالة sendErrorResponse مفقودة</span>\n";
}

if (function_exists('sendSuccessResponse')) {
    echo "<span class='success'>✅ دالة sendSuccessResponse موجودة</span>\n";
} else {
    echo "<span class='error'>❌ دالة sendSuccessResponse مفقودة</span>\n";
}

echo "</pre></div>";

echo "<div class='test-section'>";
echo "<h3>5. اختبار CSRF Token</h3>";
echo "<pre>";

try {
    $token = generateCSRFToken();
    echo "<span class='success'>✅ تم إنشاء CSRF Token: " . substr($token, 0, 20) . "...</span>\n";
    
    $isValid = verifyCSRFToken($token);
    if ($isValid) {
        echo "<span class='success'>✅ CSRF Token صحيح</span>\n";
    } else {
        echo "<span class='error'>❌ CSRF Token غير صحيح</span>\n";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في CSRF Token: " . $e->getMessage() . "</span>\n";
}

echo "</pre></div>";

echo "<div class='test-section'>";
echo "<h3>6. اختبار مجلد uploads</h3>";
echo "<pre>";

$uploadsDir = 'uploads';
if (!is_dir($uploadsDir)) {
    echo "<span class='warning'>⚠️ مجلد uploads غير موجود، سيتم إنشاؤه...</span>\n";
    if (mkdir($uploadsDir, 0755, true)) {
        echo "<span class='success'>✅ تم إنشاء مجلد uploads</span>\n";
    } else {
        echo "<span class='error'>❌ فشل في إنشاء مجلد uploads</span>\n";
    }
} else {
    echo "<span class='success'>✅ مجلد uploads موجود</span>\n";
}

if (is_writable($uploadsDir)) {
    echo "<span class='success'>✅ مجلد uploads قابل للكتابة</span>\n";
} else {
    echo "<span class='error'>❌ مجلد uploads غير قابل للكتابة</span>\n";
}

echo "</pre></div>";

echo "<div class='test-section'>";
echo "<h3>7. محاكاة طلب رفع</h3>";
echo "<pre>";

echo "محاكاة طلب POST إلى api/upload.php...\n";

// إنشاء ملف تجريبي مؤقت
$testContent = "الاسم,العمر,المدينة\nأحمد,25,الرياض\nفاطمة,30,جدة";
$tempFile = tempnam(sys_get_temp_dir(), 'csv_test');
file_put_contents($tempFile, $testContent);

// محاكاة $_FILES
$_FILES = [
    'csv_file' => [
        'name' => 'test.csv',
        'type' => 'text/csv',
        'tmp_name' => $tempFile,
        'error' => UPLOAD_ERR_OK,
        'size' => strlen($testContent)
    ]
];

// محاكاة $_POST
$_POST = [
    'csrf_token' => generateCSRFToken(),
    'delimiter' => ',',
    'encoding' => 'UTF-8'
];

// محاكاة $_SERVER
$_SERVER['REQUEST_METHOD'] = 'POST';

echo "<span class='info'>📋 ملف تجريبي: " . strlen($testContent) . " بايت</span>\n";
echo "<span class='info'>📋 CSRF Token: " . substr($_POST['csrf_token'], 0, 20) . "...</span>\n";

// اختبار معالجة الرفع
try {
    $importer = new CSVImporter();
    $result = $importer->importFile($_FILES['csv_file'], [
        'delimiter' => $_POST['delimiter'],
        'encoding' => $_POST['encoding']
    ]);
    
    if ($result['success']) {
        echo "<span class='success'>✅ تم اختبار الرفع بنجاح</span>\n";
        echo "<span class='info'>📊 النتيجة: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "</span>\n";
    } else {
        echo "<span class='error'>❌ فشل اختبار الرفع: " . $result['error'] . "</span>\n";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في اختبار الرفع: " . $e->getMessage() . "</span>\n";
}

// تنظيف الملف المؤقت
unlink($tempFile);

echo "</pre></div>";

echo "<div class='test-section'>";
echo "<h3>8. اختبار مباشر لـ API</h3>";
echo "<div id='apiTest'>";
echo "<button onclick='testUploadAPI()' style='background: #2563eb; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>اختبار API مباشر</button>";
echo "<div id='apiResult' style='margin-top: 10px;'></div>";
echo "</div>";
echo "</div>";

// أزرار التنقل
echo "<div style='margin-top: 20px; text-align: center;'>";
echo "<a href='index.php' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>الصفحة الرئيسية</a> ";
echo "<a href='simple_test.php' style='background: #d97706; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>اختبار النظام</a> ";
echo "<a href='quick_setup.php' style='background: #dc2626; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>إعداد سريع</a>";
echo "</div>";
?>

<script>
async function testUploadAPI() {
    const resultDiv = document.getElementById('apiResult');
    resultDiv.innerHTML = '<div style="color: #2563eb;">جاري الاختبار...</div>';
    
    try {
        // إنشاء ملف تجريبي
        const csvContent = 'الاسم,العمر,المدينة\nأحمد,25,الرياض\nفاطمة,30,جدة';
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const file = new File([blob], 'test.csv', { type: 'text/csv' });
        
        // إنشاء FormData
        const formData = new FormData();
        formData.append('csv_file', file);
        formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');
        formData.append('delimiter', ',');
        formData.append('encoding', 'UTF-8');
        
        // إرسال الطلب
        const response = await fetch('api/upload.php', {
            method: 'POST',
            body: formData
        });
        
        // قراءة النتيجة
        const text = await response.text();
        console.log('Response text:', text);
        
        try {
            const result = JSON.parse(text);
            if (result.success) {
                resultDiv.innerHTML = '<div style="color: #059669; font-weight: bold;">✅ نجح الاختبار!</div><pre>' + JSON.stringify(result, null, 2) + '</pre>';
            } else {
                resultDiv.innerHTML = '<div style="color: #dc2626; font-weight: bold;">❌ فشل الاختبار: ' + result.error + '</div>';
            }
        } catch (jsonError) {
            resultDiv.innerHTML = '<div style="color: #dc2626; font-weight: bold;">❌ خطأ في JSON:</div><pre>' + text + '</pre>';
        }
        
    } catch (error) {
        resultDiv.innerHTML = '<div style="color: #dc2626; font-weight: bold;">❌ خطأ في الطلب: ' + error.message + '</div>';
    }
}
</script>
