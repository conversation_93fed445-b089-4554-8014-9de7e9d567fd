<?php
/**
 * اختبار مبسط لقاعدة البيانات
 * Simple Database Test
 */

require_once 'config/config.php';

echo "<h2>اختبار مبسط لقاعدة البيانات</h2>";
echo "<style>
    .success { color: #059669; font-weight: bold; }
    .error { color: #dc2626; font-weight: bold; }
    .info { color: #2563eb; }
    pre { background: #f8fafc; padding: 15px; border-radius: 8px; margin: 10px 0; }
    .test-item { margin: 10px 0; padding: 10px; border-left: 4px solid #e2e8f0; }
    .test-item.success { border-left-color: #059669; background: #f0fdf4; }
    .test-item.error { border-left-color: #dc2626; background: #fef2f2; }
</style>";

echo "<pre>";

// اختبار 1: الاتصال بقاعدة البيانات
echo "1. اختبار الاتصال بقاعدة البيانات...\n";
try {
    $db = getDB();
    echo "<span class='success'>✅ تم الاتصال بنجاح</span>\n\n";
} catch (Exception $e) {
    echo "<span class='error'>❌ فشل الاتصال: " . $e->getMessage() . "</span>\n\n";
    exit;
}

// اختبار 2: الحصول على قائمة الجداول
echo "2. الحصول على قائمة الجداول...\n";
try {
    $tables = $db->getAllTables();
    echo "<span class='success'>✅ تم العثور على " . count($tables) . " جدول</span>\n";
    if (!empty($tables)) {
        echo "<span class='info'>الجداول الموجودة: " . implode(', ', $tables) . "</span>\n";
    }
    echo "\n";
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في الحصول على قائمة الجداول: " . $e->getMessage() . "</span>\n\n";
}

// اختبار 3: فحص الجداول المطلوبة
echo "3. فحص الجداول المطلوبة...\n";
$requiredTables = ['imported_files', 'system_settings', 'activity_log', 'user_sessions'];
$missingTables = [];

foreach ($requiredTables as $table) {
    try {
        $exists = $db->tableExists($table);
        if ($exists) {
            echo "<span class='success'>✅ جدول $table موجود</span>\n";
            
            // اختبار الوصول للجدول
            try {
                $count = $db->fetchOne("SELECT COUNT(*) as count FROM `$table`");
                $recordCount = $count ? $count['count'] : 0;
                echo "<span class='info'>   📊 يحتوي على $recordCount سجل</span>\n";
            } catch (Exception $e) {
                echo "<span class='error'>   ❌ لا يمكن الوصول للجدول: " . $e->getMessage() . "</span>\n";
            }
        } else {
            echo "<span class='error'>❌ جدول $table مفقود</span>\n";
            $missingTables[] = $table;
        }
    } catch (Exception $e) {
        echo "<span class='error'>❌ خطأ في فحص جدول $table: " . $e->getMessage() . "</span>\n";
        $missingTables[] = $table;
    }
}

echo "\n";

// اختبار 4: اختبار الإعدادات
echo "4. اختبار الإعدادات...\n";
try {
    if (in_array('system_settings', $tables ?? [])) {
        $settings = $db->fetchAll("SELECT setting_key, setting_value FROM system_settings LIMIT 5");
        if (!empty($settings)) {
            echo "<span class='success'>✅ تم العثور على " . count($settings) . " إعداد</span>\n";
            foreach ($settings as $setting) {
                echo "<span class='info'>   - {$setting['setting_key']}: {$setting['setting_value']}</span>\n";
            }
        } else {
            echo "<span class='error'>❌ جدول الإعدادات فارغ</span>\n";
        }
    } else {
        echo "<span class='error'>❌ جدول system_settings غير موجود</span>\n";
    }
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في اختبار الإعدادات: " . $e->getMessage() . "</span>\n";
}

echo "\n";

// اختبار 5: اختبار إنشاء جدول تجريبي
echo "5. اختبار إنشاء جدول تجريبي...\n";
try {
    $testTable = 'test_' . time();
    $sql = "CREATE TABLE `$testTable` (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    
    $db->execute($sql);
    echo "<span class='success'>✅ تم إنشاء جدول تجريبي: $testTable</span>\n";
    
    // إدراج بيانات تجريبية
    $db->execute("INSERT INTO `$testTable` (name) VALUES (?)", ['اختبار']);
    echo "<span class='success'>✅ تم إدراج بيانات تجريبية</span>\n";
    
    // قراءة البيانات
    $testData = $db->fetchOne("SELECT * FROM `$testTable`");
    if ($testData) {
        echo "<span class='success'>✅ تم قراءة البيانات: {$testData['name']}</span>\n";
    }
    
    // حذف الجدول التجريبي
    $db->execute("DROP TABLE `$testTable`");
    echo "<span class='success'>✅ تم حذف الجدول التجريبي</span>\n";
    
} catch (Exception $e) {
    echo "<span class='error'>❌ خطأ في اختبار الجدول التجريبي: " . $e->getMessage() . "</span>\n";
}

echo "\n";

// النتيجة النهائية
echo "=== النتيجة النهائية ===\n";
if (empty($missingTables)) {
    echo "<span class='success'>🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام</span>\n";
    echo "<span class='info'>🔗 يمكنك الآن الانتقال إلى: http://localhost/CDCO/</span>\n";
} else {
    echo "<span class='error'>⚠️ هناك مشاكل تحتاج إصلاح</span>\n";
    echo "<span class='error'>الجداول المفقودة: " . implode(', ', $missingTables) . "</span>\n";
    echo "<span class='info'>💡 جرب تشغيل: http://localhost/CDCO/quick_setup.php</span>\n";
}

echo "</pre>";

// أزرار التنقل
echo "<div style='margin-top: 20px;'>";
if (empty($missingTables)) {
    echo "<a href='index.php' style='background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🏠 الصفحة الرئيسية</a> ";
    echo "<a href='test_system.php' style='background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🧪 اختبار شامل</a> ";
} else {
    echo "<a href='quick_setup.php' style='background: #dc2626; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔧 إعداد سريع</a> ";
    echo "<a href='diagnose.php' style='background: #d97706; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 تشخيص</a> ";
}
echo "<a href='database_setup.php' style='background: #6b7280; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>⚙️ إعداد متقدم</a>";
echo "</div>";
?>
