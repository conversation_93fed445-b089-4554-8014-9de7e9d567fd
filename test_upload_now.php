<?php
/**
 * اختبار رفع فوري
 * Instant Upload Test
 */
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع فوري</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .container { max-width: 600px; margin: 0 auto; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .upload-area.dragover { border-color: #007cba; background: #f0f8ff; }
        .btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .loading { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 اختبار رفع فوري</h1>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 اسحب ملف CSV هنا أو انقر للاختيار</p>
            <input type="file" id="fileInput" accept=".csv,.txt" style="display: none;">
            <button class="btn" onclick="document.getElementById('fileInput').click()">اختر ملف</button>
        </div>
        
        <div>
            <label>فاصل الأعمدة:</label>
            <select id="delimiter">
                <option value=",">فاصلة (,)</option>
                <option value=";">فاصلة منقوطة (;)</option>
                <option value="\t">تبويب (Tab)</option>
            </select>
        </div>
        
        <div style="margin: 20px 0;">
            <button class="btn" onclick="uploadFile()" id="uploadBtn">رفع الملف</button>
        </div>
        
        <div id="result"></div>
        
        <div style="margin-top: 30px;">
            <h3>📋 ملف تجريبي سريع</h3>
            <button class="btn" onclick="createTestFile()">إنشاء ملف تجريبي</button>
            <p><small>سينشئ ملف CSV تجريبي ويرفعه تلقائياً</small></p>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="index.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">العودة للصفحة الرئيسية</a>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const resultDiv = document.getElementById('result');
        
        // Drag & Drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                showResult(`تم اختيار: ${files[0].name}`, 'loading');
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                showResult(`تم اختيار: ${e.target.files[0].name}`, 'loading');
            }
        });
        
        function showResult(message, type) {
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        async function uploadFile() {
            const file = fileInput.files[0];
            if (!file) {
                showResult('❌ يرجى اختيار ملف أولاً', 'error');
                return;
            }
            
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.textContent = 'جاري الرفع...';
            
            showResult('🔄 جاري رفع الملف...', 'loading');
            
            try {
                const formData = new FormData();
                formData.append('csv_file', file);
                formData.append('delimiter', document.getElementById('delimiter').value);
                
                console.log('Uploading file:', file.name);
                
                const response = await fetch('api/simple_upload.php', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('Response status:', response.status);
                
                const responseText = await response.text();
                console.log('Response text:', responseText);
                
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (jsonError) {
                    console.error('JSON Error:', jsonError);
                    throw new Error('استجابة غير صحيحة من الخادم: ' + responseText.substring(0, 200));
                }
                
                if (result.success) {
                    showResult(`✅ ${result.message}<br>
                        📊 الجدول: ${result.data.table_name}<br>
                        📈 السجلات: ${result.data.total_rows}<br>
                        📋 الأعمدة: ${result.data.columns.join(', ')}`, 'success');
                } else {
                    showResult(`❌ ${result.error}`, 'error');
                }
                
            } catch (error) {
                console.error('Upload error:', error);
                showResult(`❌ خطأ في الرفع: ${error.message}`, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = 'رفع الملف';
            }
        }
        
        function createTestFile() {
            const csvContent = 'الاسم,العمر,المدينة,الوظيفة\nأحمد محمد,28,الرياض,مهندس\nفاطمة علي,32,جدة,طبيبة\nخالد سعد,25,الدمام,مبرمج\nنورا حسن,29,مكة,معلمة\nعبدالله يوسف,35,المدينة,محاسب';
            
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
            const file = new File([blob], 'test_data.csv', { type: 'text/csv' });
            
            // إنشاء FileList مؤقت
            const dt = new DataTransfer();
            dt.items.add(file);
            fileInput.files = dt.files;
            
            showResult('✅ تم إنشاء ملف تجريبي. انقر "رفع الملف" الآن.', 'success');
        }
    </script>
</body>
</html>
