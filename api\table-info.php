<?php
/**
 * API معلومات الجدول
 * Table Information API
 */

require_once __DIR__ . '/../config/config.php';

// التأكد من أن الطلب GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendErrorResponse('طريقة الطلب غير صحيحة', 405);
}

// الحصول على اسم الجدول
$tableName = $_GET['table'] ?? '';
$tableName = sanitizeInput($tableName);

if (empty($tableName)) {
    sendErrorResponse('اسم الجدول مطلوب');
}

try {
    $db = getDB();
    
    // التحقق من وجود الجدول
    if (!$db->tableExists($tableName)) {
        sendErrorResponse('الجدول المطلوب غير موجود');
    }
    
    // الحصول على معلومات الملف
    $fileInfo = $db->fetchOne("
        SELECT original_name, total_rows, file_size, upload_date, columns_info 
        FROM imported_files 
        WHERE table_name = ? AND status = 'completed'
    ", [$tableName]);
    
    if (!$fileInfo) {
        sendErrorResponse('معلومات الملف غير موجودة');
    }
    
    // الحصول على معلومات الأعمدة من قاعدة البيانات
    $columns = $db->getTableColumns($tableName);
    $columnNames = [];
    
    foreach ($columns as $column) {
        if (!in_array($column['Field'], ['id', 'created_at', 'updated_at'])) {
            $columnNames[] = $column['Field'];
        }
    }
    
    // الحصول على عينة من البيانات
    $sampleData = $db->fetchAll("SELECT * FROM `$tableName` LIMIT 5");
    
    // إحصائيات إضافية
    $stats = [
        'total_columns' => count($columnNames),
        'data_columns' => count($columnNames),
        'sample_rows' => count($sampleData)
    ];
    
    // إرسال النتيجة
    sendSuccessResponse([
        'file_name' => $fileInfo['original_name'],
        'table_name' => $tableName,
        'total_rows' => (int)$fileInfo['total_rows'],
        'file_size' => (int)$fileInfo['file_size'],
        'upload_date' => $fileInfo['upload_date'],
        'columns' => $columnNames,
        'sample_data' => $sampleData,
        'stats' => $stats
    ]);
    
} catch (Exception $e) {
    error_log("Table info error: " . $e->getMessage());
    sendErrorResponse('حدث خطأ أثناء تحميل معلومات الجدول: ' . $e->getMessage());
}
?>
