<?php
/**
 * الإعدادات العامة للنظام
 * General System Configuration
 */

// بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// إعدادات المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// إعدادات الترميز
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');

// إعدادات الأخطاء (للتطوير)
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/error.log');

// إعدادات رفع الملفات
ini_set('upload_max_filesize', '250M');
ini_set('post_max_size', '250M');
ini_set('max_execution_time', 600); // 10 دقائق
ini_set('memory_limit', '512M');

// المسارات
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/config');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('LOGS_PATH', ROOT_PATH . '/logs');
define('ASSETS_PATH', ROOT_PATH . '/assets');

// إنشاء المجلدات إذا لم تكن موجودة
$directories = [UPLOADS_PATH, LOGS_PATH];
foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// إعدادات النظام
define('SITE_TITLE', 'مدير ملفات CSV');
define('SITE_DESCRIPTION', 'نظام إدارة واستيراد ملفات CSV مع الاستعلام الفوري');
define('DEFAULT_LANGUAGE', 'ar');
define('RECORDS_PER_PAGE', 50);
define('MAX_FILE_SIZE', 250 * 1024 * 1024); // 250 MB
define('ALLOWED_EXTENSIONS', ['csv', 'txt']);

// إعدادات قاعدة البيانات
require_once CONFIG_PATH . '/database.php';

/**
 * دالة تسجيل الأنشطة
 */
function logActivity($action, $tableName = null, $description = null) {
    try {
        $db = getDB();
        $sql = "INSERT INTO activity_log (action_type, table_name, description, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?)";
        
        $params = [
            $action,
            $tableName,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];
        
        $db->execute($sql, $params);
    } catch (Exception $e) {
        error_log("Error logging activity: " . $e->getMessage());
    }
}

/**
 * دالة تنظيف البيانات
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * دالة التحقق من صحة امتداد الملف
 */
function isValidFileExtension($filename) {
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    return in_array($extension, ALLOWED_EXTENSIONS);
}

/**
 * دالة تحويل حجم الملف إلى نص قابل للقراءة
 */
function formatFileSize($bytes) {
    $units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

/**
 * دالة إنشاء اسم جدول فريد
 */
function generateTableName($filename) {
    $name = pathinfo($filename, PATHINFO_FILENAME);
    $name = preg_replace('/[^\p{L}\p{N}_]/u', '_', $name);
    $name = preg_replace('/_+/', '_', $name);
    $name = trim($name, '_');
    
    if (empty($name)) {
        $name = 'table';
    }
    
    // إضافة timestamp لضمان الفرادة
    $name .= '_' . time();
    
    return strtolower($name);
}

/**
 * دالة إرسال استجابة JSON
 */
function sendJsonResponse($data, $statusCode = 200) {
    // تنظيف أي مخرجات سابقة
    if (ob_get_level()) {
        ob_clean();
    }

    // تعيين headers
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');

    // إرسال JSON
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * دالة إرسال رسالة خطأ JSON
 */
function sendErrorResponse($message, $statusCode = 400) {
    sendJsonResponse([
        'success' => false,
        'error' => $message
    ], $statusCode);
}

/**
 * دالة إرسال رسالة نجاح JSON
 */
function sendSuccessResponse($data = [], $message = 'تم بنجاح') {
    sendJsonResponse([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
}

/**
 * دالة الحصول على إعدادات النظام
 */
function getSystemSettings() {
    static $settings = null;
    
    if ($settings === null) {
        try {
            $db = getDB();
            $result = $db->fetchAll("SELECT setting_key, setting_value FROM system_settings");
            $settings = [];
            foreach ($result as $row) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (Exception $e) {
            $settings = [];
        }
    }
    
    return $settings;
}

/**
 * دالة الحصول على إعداد محدد
 */
function getSetting($key, $default = null) {
    $settings = getSystemSettings();
    return $settings[$key] ?? $default;
}

/**
 * دالة حماية من CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * دالة التحقق من CSRF Token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// إنشاء CSRF Token
generateCSRFToken();
?>
