<?php
/**
 * إعداد سريع لقاعدة البيانات
 * Quick Database Setup
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_name = 'csv_manager';
$db_user = 'root';
$db_pass = '';

echo "<h2>إعداد سريع لقاعدة البيانات</h2>";
echo "<pre>";

try {
    // الاتصال بخادم MySQL
    echo "1. الاتصال بخادم MySQL...\n";
    $pdo = new PDO("mysql:host=$db_host;charset=utf8mb4", $db_user, $db_pass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "✅ تم الاتصال بنجاح\n\n";
    
    // إنشاء قاعدة البيانات
    echo "2. إنشاء قاعدة البيانات...\n";
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $pdo->exec("USE `$db_name`");
    echo "✅ تم إنشاء قاعدة البيانات: $db_name\n\n";
    
    // إنشاء الجداول
    echo "3. إنشاء الجداول...\n";
    
    // جدول imported_files
    $sql = "CREATE TABLE IF NOT EXISTS imported_files (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_name VARCHAR(255) NOT NULL,
        original_name VARCHAR(255) NOT NULL,
        table_name VARCHAR(255) NOT NULL UNIQUE,
        file_size BIGINT NOT NULL,
        total_rows INT DEFAULT 0,
        columns_info JSON,
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        status ENUM('processing', 'completed', 'error') DEFAULT 'processing',
        error_message TEXT NULL,
        INDEX idx_table_name (table_name),
        INDEX idx_upload_date (upload_date),
        INDEX idx_status (status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول: imported_files\n";
    
    // جدول system_settings
    $sql = "CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول: system_settings\n";
    
    // جدول activity_log
    $sql = "CREATE TABLE IF NOT EXISTS activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        action_type ENUM('upload', 'delete', 'update', 'search', 'export') NOT NULL,
        table_name VARCHAR(255),
        description TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_action_type (action_type),
        INDEX idx_table_name (table_name),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول: activity_log\n";
    
    // جدول user_sessions
    $sql = "CREATE TABLE IF NOT EXISTS user_sessions (
        id VARCHAR(128) PRIMARY KEY,
        session_data TEXT,
        last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        user_agent TEXT,
        INDEX idx_last_activity (last_activity)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ تم إنشاء جدول: user_sessions\n\n";
    
    // إدراج الإعدادات الافتراضية
    echo "4. إدراج الإعدادات الافتراضية...\n";
    
    $settings = [
        ['max_file_size', '250', 'الحد الأقصى لحجم الملف بالميجابايت'],
        ['allowed_extensions', 'csv,txt', 'امتدادات الملفات المسموحة'],
        ['records_per_page', '50', 'عدد السجلات في الصفحة الواحدة'],
        ['site_title', 'مدير ملفات CSV', 'عنوان الموقع'],
        ['site_description', 'نظام إدارة واستيراد ملفات CSV مع الاستعلام الفوري', 'وصف الموقع'],
        ['default_language', 'ar', 'اللغة الافتراضية للموقع'],
        ['date_format', 'Y-m-d H:i:s', 'تنسيق التاريخ'],
        ['timezone', 'Asia/Riyadh', 'المنطقة الزمنية']
    ];
    
    $stmt = $pdo->prepare("INSERT INTO system_settings (setting_key, setting_value, description) 
                          VALUES (?, ?, ?) 
                          ON DUPLICATE KEY UPDATE 
                          setting_value = VALUES(setting_value),
                          updated_at = CURRENT_TIMESTAMP");
    
    $inserted = 0;
    foreach ($settings as $setting) {
        $stmt->execute($setting);
        $inserted++;
        echo "✅ إعداد: {$setting[0]} = {$setting[1]}\n";
    }
    
    echo "\n5. التحقق من الجداول...\n";
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($tables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
        echo "✅ جدول: $table ($count سجل)\n";
    }
    
    // تحديث ملف الإعدادات
    echo "\n6. تحديث ملف الإعدادات...\n";
    $config_file = 'config/database.php';
    if (file_exists($config_file)) {
        $config_content = file_get_contents($config_file);
        
        $config_content = preg_replace(
            "/define\('DB_HOST',\s*'[^']*'\);/",
            "define('DB_HOST', '$db_host');",
            $config_content
        );
        $config_content = preg_replace(
            "/define\('DB_NAME',\s*'[^']*'\);/",
            "define('DB_NAME', '$db_name');",
            $config_content
        );
        $config_content = preg_replace(
            "/define\('DB_USER',\s*'[^']*'\);/",
            "define('DB_USER', '$db_user');",
            $config_content
        );
        $config_content = preg_replace(
            "/define\('DB_PASS',\s*'[^']*'\);/",
            "define('DB_PASS', '$db_pass');",
            $config_content
        );
        
        if (file_put_contents($config_file, $config_content)) {
            echo "✅ تم تحديث ملف الإعدادات\n";
        } else {
            echo "❌ فشل في تحديث ملف الإعدادات\n";
        }
    } else {
        echo "❌ ملف الإعدادات غير موجود\n";
    }
    
    // اختبار النظام
    echo "\n7. اختبار النظام...\n";
    $test_query = $pdo->query("SELECT COUNT(*) as count FROM system_settings")->fetch();
    if ($test_query && $test_query['count'] > 0) {
        echo "✅ النظام يعمل بشكل صحيح - تم العثور على {$test_query['count']} إعداد\n";
    } else {
        echo "❌ مشكلة في النظام\n";
    }
    
    echo "\n🎉 تم إعداد قاعدة البيانات بنجاح!\n\n";
    echo "الخطوات التالية:\n";
    echo "1. انتقل إلى: http://localhost/CDCO/\n";
    echo "2. جرب رفع ملف test.csv\n";
    echo "3. اختبر النظام: http://localhost/CDCO/test_system.php\n\n";
    
} catch (PDOException $e) {
    echo "❌ خطأ: " . $e->getMessage() . "\n";
    echo "\nتأكد من:\n";
    echo "1. تشغيل خادم MySQL\n";
    echo "2. صحة اسم المستخدم وكلمة المرور\n";
    echo "3. صلاحيات إنشاء قواعد البيانات\n";
} catch (Exception $e) {
    echo "❌ خطأ عام: " . $e->getMessage() . "\n";
}

echo "</pre>";

// إضافة أزرار للتنقل
echo '<div style="margin-top: 20px;">';
echo '<a href="index.php" style="background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">الصفحة الرئيسية</a> ';
echo '<a href="test_system.php" style="background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">اختبار النظام</a> ';
echo '<a href="database_setup.php" style="background: #dc2626; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">الإعداد المتقدم</a>';
echo '</div>';
?>
