-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS csv_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE csv_manager;

-- جدول معلومات الملفات المستوردة
CREATE TABLE IF NOT EXISTS imported_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    table_name VARCHAR(255) NOT NULL UNIQUE,
    file_size BIGINT NOT NULL,
    total_rows INT DEFAULT 0,
    columns_info JSON,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('processing', 'completed', 'error') DEFAULT 'processing',
    error_message TEXT NULL,
    INDEX idx_table_name (table_name),
    INDEX idx_upload_date (upload_date),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الإعدادات الافتراضية
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('max_file_size', '50', 'الحد الأقصى لحجم الملف بالميجابايت'),
('allowed_extensions', 'csv,txt', 'امتدادات الملفات المسموحة'),
('records_per_page', '50', 'عدد السجلات في الصفحة الواحدة'),
('site_title', 'مدير ملفات CSV', 'عنوان الموقع'),
('site_description', 'نظام إدارة واستيراد ملفات CSV مع الاستعلام الفوري', 'وصف الموقع'),
('default_language', 'ar', 'اللغة الافتراضية للموقع'),
('date_format', 'Y-m-d H:i:s', 'تنسيق التاريخ'),
('timezone', 'Asia/Riyadh', 'المنطقة الزمنية')
ON DUPLICATE KEY UPDATE 
setting_value = VALUES(setting_value),
updated_at = CURRENT_TIMESTAMP;

-- جدول سجل العمليات
CREATE TABLE IF NOT EXISTS activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    action_type ENUM('upload', 'delete', 'update', 'search', 'export') NOT NULL,
    table_name VARCHAR(255),
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_action_type (action_type),
    INDEX idx_table_name (table_name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول الجلسات (للمستقبل إذا أردنا إضافة نظام مستخدمين)
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(128) PRIMARY KEY,
    session_data TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent TEXT,
    INDEX idx_last_activity (last_activity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات (اختياري)
-- CREATE USER IF NOT EXISTS 'csv_user'@'localhost' IDENTIFIED BY 'csv_password_2024';
-- GRANT ALL PRIVILEGES ON csv_manager.* TO 'csv_user'@'localhost';
-- FLUSH PRIVILEGES;
