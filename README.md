# مدير ملفات CSV - نظام إدارة واستيراد ملفات CSV

نظام ويب متكامل لاستيراد وإدارة ملفات CSV مع دعم كامل للغة العربية والاستعلام الفوري.

## الميزات الرئيسية

### 🚀 استيراد متقدم
- استيراد ملفات CSV و TXT بدون قيود
- إنشاء جداول ديناميكية تلقائياً
- دعم ترميزات متعددة (UTF-8, Windows-1256, ISO-8859-6)
- معالجة فواصل مختلفة (فاصلة، فاصلة منقوطة، تبويب)
- رفع ملفات حتى 50 ميجابايت

### 🔍 بحث واستعلام فوري
- بحث فوري في جميع الأعمدة
- فلترة وترتيب متقدم
- تنقل بين الصفحات
- بحث متقدم مع خيارات متعددة

### 📊 إدارة البيانات
- عرض البيانات في جداول تفاعلية
- تصدير بصيغ متعددة (CSV, Excel, JSON)
- حذف الملفات والبيانات
- إحصائيات مفصلة

### 🎨 واجهة مودرن
- تصميم متجاوب يدعم جميع الأجهزة
- دعم كامل للغة العربية (RTL)
- واجهة مستخدم بديهية
- ألوان وأيقونات حديثة

### 🔒 الأمان
- حماية CSRF
- تنظيف المدخلات
- تسجيل الأنشطة
- معالجة الأخطاء

## متطلبات النظام

- **خادم ويب**: Apache أو Nginx
- **PHP**: الإصدار 7.4 أو أحدث
- **قاعدة البيانات**: MySQL 5.7 أو أحدث / MariaDB 10.2 أو أحدث
- **امتدادات PHP المطلوبة**:
  - PDO
  - PDO_MySQL
  - mbstring
  - json
  - fileinfo

## التثبيت

### 1. تحضير البيئة

```bash
# تأكد من تشغيل XAMPP أو WAMP أو LAMP
# تأكد من تفعيل Apache و MySQL
```

### 2. نسخ الملفات

```bash
# انسخ جميع ملفات المشروع إلى مجلد htdocs
cp -r CDCO/ /path/to/xampp/htdocs/
```

### 3. إعداد قاعدة البيانات

1. افتح phpMyAdmin أو أي أداة إدارة MySQL
2. قم بتشغيل الاستعلامات الموجودة في `database/setup.sql`

```sql
-- أو استخدم سطر الأوامر
mysql -u root -p < database/setup.sql
```

### 4. تكوين الاتصال

عدّل ملف `config/database.php` حسب إعدادات قاعدة البيانات:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'csv_manager');
define('DB_USER', 'root');
define('DB_PASS', ''); // كلمة المرور
```

### 5. ضبط الصلاحيات

```bash
# تأكد من صلاحيات الكتابة للمجلدات التالية
chmod 755 uploads/
chmod 755 logs/
```

## الاستخدام

### 1. الوصول للنظام
افتح المتصفح وانتقل إلى: `http://localhost/CDCO`

### 2. رفع ملف CSV
1. انقر على "رفع ملف CSV جديد"
2. اختر الملف أو اسحبه إلى المنطقة المخصصة
3. اختر إعدادات الاستيراد (فاصل الأعمدة، الترميز)
4. انقر "رفع الملف"

### 3. عرض البيانات
1. انتقل إلى "الملفات"
2. انقر على "عرض" بجانب الملف المطلوب
3. استخدم البحث والفلترة حسب الحاجة

### 4. البحث المتقدم
1. انتقل إلى "البحث"
2. اختر الملف للبحث فيه
3. أدخل كلمة البحث
4. اختر خيارات الترتيب والعرض

### 5. تصدير البيانات
- من صفحة عرض البيانات، انقر على "تصدير CSV" أو "تصدير Excel"
- أو من صفحة الملفات، انقر على أيقونة التحميل

## هيكل المشروع

```
CDCO/
├── api/                    # واجهات برمجة التطبيقات
│   ├── delete.php         # حذف الملفات
│   ├── export.php         # تصدير البيانات
│   ├── search.php         # البحث والاستعلام
│   ├── stats.php          # الإحصائيات
│   ├── table-info.php     # معلومات الجداول
│   └── upload.php         # رفع الملفات
├── assets/                # الموارد الثابتة
│   ├── css/
│   │   └── style.css      # الأنماط الرئيسية
│   └── js/
│       └── main.js        # JavaScript الرئيسي
├── config/                # ملفات التكوين
│   ├── config.php         # الإعدادات العامة
│   └── database.php       # إعدادات قاعدة البيانات
├── database/              # قاعدة البيانات
│   └── setup.sql          # إعداد قاعدة البيانات
├── includes/              # الملفات المساعدة
│   └── CSVImporter.php    # فئة استيراد CSV
├── logs/                  # ملفات السجلات
├── uploads/               # الملفات المرفوعة
├── files.php              # صفحة إدارة الملفات
├── index.php              # الصفحة الرئيسية
├── search.php             # صفحة البحث المتقدم
├── view.php               # صفحة عرض البيانات
└── README.md              # هذا الملف
```

## الأمان والأداء

### نصائح الأمان
- غيّر كلمات المرور الافتراضية
- استخدم HTTPS في الإنتاج
- قم بتحديث PHP و MySQL بانتظام
- راجع سجلات الأخطاء دورياً

### تحسين الأداء
- استخدم فهرسة قاعدة البيانات للجداول الكبيرة
- قم بضغط الملفات الثابتة
- استخدم التخزين المؤقت للاستعلامات المتكررة

## استكشاف الأخطاء

### مشاكل شائعة

**خطأ في الاتصال بقاعدة البيانات**
- تأكد من تشغيل MySQL
- تحقق من إعدادات الاتصال في `config/database.php`

**فشل في رفع الملف**
- تحقق من صلاحيات مجلد `uploads/`
- تأكد من أن حجم الملف أقل من 50 ميجابايت
- تحقق من إعدادات PHP: `upload_max_filesize` و `post_max_size`

**مشاكل في عرض العربية**
- تأكد من أن قاعدة البيانات تستخدم ترميز `utf8mb4`
- تحقق من إعدادات المتصفح للترميز

## الدعم والمساهمة

### الإبلاغ عن الأخطاء
إذا واجهت أي مشاكل، يرجى التحقق من:
1. ملف السجل: `logs/error.log`
2. سجل أخطاء Apache/Nginx
3. سجل أخطاء MySQL

### التطوير
المشروع مفتوح المصدر ويرحب بالمساهمات:
- تحسين الأداء
- إضافة ميزات جديدة
- إصلاح الأخطاء
- تحسين التوثيق

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الإصدار

الإصدار الحالي: 1.0.0

### سجل التغييرات
- **1.0.0**: الإصدار الأول
  - استيراد ملفات CSV
  - بحث واستعلام فوري
  - تصدير البيانات
  - واجهة عربية مودرن
  - نظام إدارة متكامل

---

**تم تطويره بواسطة**: Augment Agent  
**التاريخ**: 2024  
**اللغة**: PHP, MySQL, HTML, CSS, JavaScript
