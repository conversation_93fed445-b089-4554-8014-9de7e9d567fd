<?php
/**
 * إعدادات قاعدة البيانات
 * Database Configuration
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'csv_manager');
define('DB_USER', 'root'); // أو csv_user إذا تم إنشاء مستخدم منفصل
define('DB_PASS', ''); // كلمة المرور
define('DB_CHARSET', 'utf8mb4');

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
        } catch (PDOException $e) {
            die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * تنفيذ استعلام مع معاملات
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Database Error: " . $e->getMessage());
            throw new Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على سجل واحد
     */
    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * الحصول على جميع السجلات
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * إدراج سجل جديد والحصول على ID
     */
    public function insert($sql, $params = []) {
        $this->query($sql, $params);
        return $this->connection->lastInsertId();
    }
    
    /**
     * تحديث أو حذف سجلات
     */
    public function execute($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * بدء معاملة
     */
    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }
    
    /**
     * تأكيد المعاملة
     */
    public function commit() {
        return $this->connection->commit();
    }
    
    /**
     * إلغاء المعاملة
     */
    public function rollback() {
        return $this->connection->rollback();
    }
    
    /**
     * التحقق من وجود جدول
     */
    public function tableExists($tableName) {
        $sql = "SHOW TABLES LIKE ?";
        $result = $this->fetchOne($sql, [$tableName]);
        return !empty($result);
    }
    
    /**
     * الحصول على معلومات أعمدة الجدول
     */
    public function getTableColumns($tableName) {
        $sql = "DESCRIBE `$tableName`";
        return $this->fetchAll($sql);
    }
    
    /**
     * إنشاء جدول ديناميكي من بيانات CSV
     */
    public function createDynamicTable($tableName, $columns) {
        $sql = "CREATE TABLE IF NOT EXISTS `$tableName` (";
        $sql .= "id INT AUTO_INCREMENT PRIMARY KEY,";
        
        foreach ($columns as $column) {
            $columnName = $this->sanitizeColumnName($column);
            $sql .= "`$columnName` TEXT,";
        }
        
        $sql .= "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,";
        $sql .= "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
        $sql .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        return $this->execute($sql);
    }
    
    /**
     * تنظيف اسم العمود
     */
    private function sanitizeColumnName($name) {
        // إزالة المسافات والرموز الخاصة
        $name = trim($name);
        $name = preg_replace('/[^\p{L}\p{N}_]/u', '_', $name);
        $name = preg_replace('/_+/', '_', $name);
        $name = trim($name, '_');
        
        // التأكد من أن الاسم لا يبدأ برقم
        if (preg_match('/^\d/', $name)) {
            $name = 'col_' . $name;
        }
        
        // إذا كان الاسم فارغاً، استخدم اسم افتراضي
        if (empty($name)) {
            $name = 'column_' . uniqid();
        }
        
        return $name;
    }
}

// دالة مساعدة للحصول على اتصال قاعدة البيانات
function getDB() {
    return Database::getInstance();
}
?>
