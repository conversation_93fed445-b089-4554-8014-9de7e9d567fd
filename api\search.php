<?php
/**
 * API البحث والاستعلام الفوري
 * Search and Real-time Query API
 */

require_once __DIR__ . '/../config/config.php';

// التأكد من أن الطلب GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendErrorResponse('طريقة الطلب غير صحيحة', 405);
}

// الحصول على المعاملات
$tableName = $_GET['table'] ?? '';
$searchQuery = $_GET['q'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = max(1, min(100, intval($_GET['limit'] ?? RECORDS_PER_PAGE)));
$sortBy = $_GET['sort'] ?? '';
$sortOrder = strtoupper($_GET['order'] ?? 'ASC') === 'DESC' ? 'DESC' : 'ASC';

// تنظيف المدخلات
$tableName = sanitizeInput($tableName);
$searchQuery = sanitizeInput($searchQuery);
$sortBy = sanitizeInput($sortBy);

try {
    $db = getDB();
    
    // التحقق من وجود الجدول
    if (!$db->tableExists($tableName)) {
        sendErrorResponse('الجدول المطلوب غير موجود');
    }
    
    // الحصول على معلومات الأعمدة
    $columns = $db->getTableColumns($tableName);
    $columnNames = array_column($columns, 'Field');
    
    // بناء استعلام البحث
    $searchConditions = [];
    $searchParams = [];
    
    if (!empty($searchQuery)) {
        foreach ($columnNames as $column) {
            if ($column !== 'id' && $column !== 'created_at' && $column !== 'updated_at') {
                $searchConditions[] = "`$column` LIKE ?";
                $searchParams[] = "%$searchQuery%";
            }
        }
    }
    
    // بناء الاستعلام الأساسي
    $whereClause = '';
    if (!empty($searchConditions)) {
        $whereClause = 'WHERE ' . implode(' OR ', $searchConditions);
    }
    
    // ترتيب النتائج
    $orderClause = '';
    if (!empty($sortBy) && in_array($sortBy, $columnNames)) {
        $orderClause = "ORDER BY `$sortBy` $sortOrder";
    } else {
        $orderClause = "ORDER BY id DESC";
    }
    
    // حساب الإزاحة
    $offset = ($page - 1) * $limit;
    
    // استعلام العد الإجمالي
    $countSql = "SELECT COUNT(*) as total FROM `$tableName` $whereClause";
    $totalRecords = $db->fetchOne($countSql, $searchParams)['total'];
    
    // استعلام البيانات
    $dataSql = "SELECT * FROM `$tableName` $whereClause $orderClause LIMIT $limit OFFSET $offset";
    $data = $db->fetchAll($dataSql, $searchParams);
    
    // حساب معلومات الصفحات
    $totalPages = ceil($totalRecords / $limit);
    $hasNext = $page < $totalPages;
    $hasPrev = $page > 1;
    
    // تسجيل النشاط
    if (!empty($searchQuery)) {
        logActivity('search', $tableName, "بحث عن: $searchQuery");
    }
    
    // إرسال النتيجة
    sendSuccessResponse([
        'data' => $data,
        'columns' => $columnNames,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_records' => $totalRecords,
            'per_page' => $limit,
            'has_next' => $hasNext,
            'has_prev' => $hasPrev
        ],
        'search' => [
            'query' => $searchQuery,
            'results_count' => count($data)
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Search error: " . $e->getMessage());
    sendErrorResponse('حدث خطأ أثناء البحث: ' . $e->getMessage());
}
?>
