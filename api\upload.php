<?php
/**
 * معالج رفع ملفات CSV
 * CSV Upload Handler
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/CSVImporter.php';

// التأكد من أن الطلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendErrorResponse('طريقة الطلب غير صحيحة', 405);
}

// التحقق من CSRF Token
if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
    sendErrorResponse('رمز الأمان غير صحيح', 403);
}

// التحقق من وجود ملف
if (!isset($_FILES['csv_file']) || empty($_FILES['csv_file']['name'])) {
    sendErrorResponse('يرجى اختيار ملف CSV');
}

try {
    // إنشاء كائن المستورد
    $importer = new CSVImporter();
    
    // خيارات الاستيراد
    $options = [
        'delimiter' => $_POST['delimiter'] ?? ',',
        'enclosure' => $_POST['enclosure'] ?? '"',
        'escape' => $_POST['escape'] ?? '\\',
        'encoding' => $_POST['encoding'] ?? 'UTF-8'
    ];
    
    // تنظيف الخيارات
    $options = array_map('sanitizeInput', $options);
    
    // استيراد الملف
    $result = $importer->importFile($_FILES['csv_file'], $options);
    
    if ($result['success']) {
        sendSuccessResponse($result, 'تم استيراد الملف بنجاح');
    } else {
        sendErrorResponse($result['error']);
    }
    
} catch (Exception $e) {
    error_log("Upload error: " . $e->getMessage());
    sendErrorResponse('حدث خطأ أثناء استيراد الملف: ' . $e->getMessage());
}
?>
