<?php
/**
 * معالج رفع ملفات CSV
 * CSV Upload Handler
 */

// تعطيل عرض الأخطاء وإعادة توجيهها للسجل
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// تعيين نوع المحتوى كـ JSON
header('Content-Type: application/json; charset=utf-8');

// بدء output buffering لمنع أي مخرجات غير مرغوبة
ob_start();

try {
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/../includes/CSVImporter.php';
} catch (Exception $e) {
    // تنظيف أي مخرجات سابقة
    ob_clean();

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'خطأ في تحميل ملفات النظام: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// التأكد من أن الطلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendErrorResponse('طريقة الطلب غير صحيحة', 405);
}

// التحقق من CSRF Token
if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
    sendErrorResponse('رمز الأمان غير صحيح', 403);
}

// التحقق من وجود ملف
if (!isset($_FILES['csv_file']) || empty($_FILES['csv_file']['name'])) {
    sendErrorResponse('يرجى اختيار ملف CSV');
}

try {
    // إنشاء كائن المستورد
    $importer = new CSVImporter();
    
    // خيارات الاستيراد
    $options = [
        'delimiter' => $_POST['delimiter'] ?? ',',
        'enclosure' => $_POST['enclosure'] ?? '"',
        'escape' => $_POST['escape'] ?? '\\',
        'encoding' => $_POST['encoding'] ?? 'UTF-8'
    ];
    
    // تنظيف الخيارات
    $options = array_map('sanitizeInput', $options);
    
    // استيراد الملف
    $result = $importer->importFile($_FILES['csv_file'], $options);
    
    if ($result['success']) {
        sendSuccessResponse($result, 'تم استيراد الملف بنجاح');
    } else {
        sendErrorResponse($result['error']);
    }
    
} catch (Exception $e) {
    // تنظيف أي مخرجات سابقة
    ob_clean();

    error_log("Upload error: " . $e->getMessage());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ أثناء استيراد الملف: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// تنظيف output buffer وإرسال النتيجة
ob_end_clean();
?>
