<?php
/**
 * صفحة عرض البيانات
 * Data View Page
 */

require_once 'config/config.php';

// الحصول على اسم الجدول
$tableName = $_GET['table'] ?? '';
$tableName = sanitizeInput($tableName);

if (empty($tableName)) {
    header('Location: files.php');
    exit;
}

try {
    $db = getDB();
    
    // التحقق من وجود الجدول
    if (!$db->tableExists($tableName)) {
        throw new Exception('الجدول المطلوب غير موجود');
    }
    
    // الحصول على معلومات الملف
    $fileInfo = $db->fetchOne("
        SELECT original_name, total_rows, file_size, upload_date 
        FROM imported_files 
        WHERE table_name = ? AND status = 'completed'
    ", [$tableName]);
    
    if (!$fileInfo) {
        throw new Exception('معلومات الملف غير موجودة');
    }
    
    // الحصول على معلومات الأعمدة
    $columns = $db->getTableColumns($tableName);
    $columnNames = array_column($columns, 'Field');
    
    // إزالة الأعمدة النظام
    $dataColumns = array_filter($columnNames, function($col) {
        return !in_array($col, ['id', 'created_at', 'updated_at']);
    });
    
} catch (Exception $e) {
    $error = $e->getMessage();
}

$pageTitle = 'عرض البيانات - ' . ($fileInfo['original_name'] ?? 'غير معروف');
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <title><?php echo $pageTitle . ' - ' . SITE_TITLE; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-file-csv"></i>
                    <?php echo SITE_TITLE; ?>
                </div>
                <nav class="nav">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                    <a href="files.php" class="nav-link">
                        <i class="fas fa-folder"></i>
                        الملفات
                    </a>
                    <a href="search.php" class="nav-link">
                        <i class="fas fa-search"></i>
                        البحث
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="container">
            <?php if (isset($error)): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
            <div class="text-center">
                <a href="files.php" class="btn btn-primary">العودة إلى الملفات</a>
            </div>
            <?php else: ?>

            <!-- معلومات الملف -->
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">
                        <i class="fas fa-table"></i>
                        <?php echo htmlspecialchars($fileInfo['original_name']); ?>
                    </h1>
                    <p class="card-description">
                        عرض وإدارة بيانات الملف المستورد
                    </p>
                </div>
                
                <div class="grid grid-4">
                    <div class="text-center">
                        <div class="stat-number text-primary"><?php echo number_format($fileInfo['total_rows']); ?></div>
                        <div class="stat-label">إجمالي السجلات</div>
                    </div>
                    <div class="text-center">
                        <div class="stat-number text-secondary"><?php echo count($dataColumns); ?></div>
                        <div class="stat-label">عدد الأعمدة</div>
                    </div>
                    <div class="text-center">
                        <div class="stat-number text-info"><?php echo formatFileSize($fileInfo['file_size']); ?></div>
                        <div class="stat-label">حجم الملف</div>
                    </div>
                    <div class="text-center">
                        <div class="stat-number text-success"><?php echo date('Y-m-d', strtotime($fileInfo['upload_date'])); ?></div>
                        <div class="stat-label">تاريخ الرفع</div>
                    </div>
                </div>
            </div>

            <!-- أدوات التحكم -->
            <div class="card">
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                    <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                        <div class="form-group" style="margin: 0; min-width: 200px;">
                            <input type="text" id="searchInput" class="form-input" 
                                   placeholder="بحث في البيانات...">
                        </div>
                        <button id="clearSearch" class="btn btn-outline">
                            <i class="fas fa-times"></i>
                            مسح
                        </button>
                    </div>
                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                        <button onclick="exportData('csv')" class="btn btn-outline btn-sm">
                            <i class="fas fa-download"></i>
                            تصدير CSV
                        </button>
                        <button onclick="exportData('excel')" class="btn btn-outline btn-sm">
                            <i class="fas fa-file-excel"></i>
                            تصدير Excel
                        </button>
                        <a href="search.php?table=<?php echo urlencode($tableName); ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-search"></i>
                            بحث متقدم
                        </a>
                    </div>
                </div>
            </div>

            <!-- جدول البيانات -->
            <div class="card">
                <div id="loadingIndicator" class="text-center p-4 hidden">
                    <div class="spinner"></div>
                    <span>جاري تحميل البيانات...</span>
                </div>

                <div id="dataContainer">
                    <div class="table-container">
                        <table id="dataTable" class="table">
                            <thead>
                                <tr>
                                    <?php foreach ($dataColumns as $column): ?>
                                    <th>
                                        <button class="sort-btn" data-column="<?php echo htmlspecialchars($column); ?>">
                                            <?php echo htmlspecialchars($column); ?>
                                            <i class="fas fa-sort"></i>
                                        </button>
                                    </th>
                                    <?php endforeach; ?>
                                </tr>
                            </thead>
                            <tbody id="dataTableBody">
                                <!-- سيتم تحميل البيانات بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- التنقل بين الصفحات -->
                <div id="paginationContainer" class="mt-3"></div>
            </div>

            <?php endif; ?>
        </div>
    </main>

    <!-- الفوتر -->
    <footer class="footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_TITLE; ?>. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        // متغيرات الصفحة
        const tableName = '<?php echo addslashes($tableName); ?>';
        let currentPage = 1;
        let currentSort = '';
        let currentOrder = 'ASC';
        let searchQuery = '';

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initializeDataView();
            loadData();
        });

        function initializeDataView() {
            // البحث الفوري
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', debounce(handleSearch, 500));

            // مسح البحث
            document.getElementById('clearSearch').addEventListener('click', clearSearch);

            // أزرار الترتيب
            document.querySelectorAll('.sort-btn').forEach(btn => {
                btn.addEventListener('click', handleSort);
            });

            // تعيين الجدول الحالي للبحث
            currentTable = tableName;
        }

        async function loadData(page = 1) {
            currentPage = page;
            
            const loadingIndicator = document.getElementById('loadingIndicator');
            const dataContainer = document.getElementById('dataContainer');
            
            try {
                loadingIndicator.classList.remove('hidden');
                dataContainer.style.opacity = '0.5';

                const params = new URLSearchParams({
                    table: tableName,
                    page: page,
                    limit: 50,
                    q: searchQuery,
                    sort: currentSort,
                    order: currentOrder
                });

                const response = await fetch(`api/search.php?${params}`);
                const result = await response.json();

                if (result.success) {
                    displayData(result.data);
                } else {
                    showAlert(result.error, 'error');
                }

            } catch (error) {
                console.error('Load data error:', error);
                showAlert('حدث خطأ أثناء تحميل البيانات', 'error');
            } finally {
                loadingIndicator.classList.add('hidden');
                dataContainer.style.opacity = '1';
            }
        }

        function displayData(data) {
            const tableBody = document.getElementById('dataTableBody');
            const paginationContainer = document.getElementById('paginationContainer');

            // عرض البيانات
            if (data.data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="100%" class="text-center">لا توجد بيانات</td></tr>';
            } else {
                tableBody.innerHTML = data.data.map(row => {
                    const cells = Object.values(row).slice(1, -2); // إزالة id و timestamps
                    return `<tr>${cells.map(cell => `<td>${escapeHtml(cell || '')}</td>`).join('')}</tr>`;
                }).join('');
            }

            // عرض التنقل بين الصفحات
            displayPagination(data.pagination);
        }

        function displayPagination(pagination) {
            const container = document.getElementById('paginationContainer');
            
            if (pagination.total_pages <= 1) {
                container.innerHTML = '';
                return;
            }

            let html = '<div style="display: flex; justify-content: center; align-items: center; gap: 1rem; flex-wrap: wrap;">';
            
            // زر السابق
            if (pagination.has_prev) {
                html += `<button onclick="loadData(${pagination.current_page - 1})" class="btn btn-outline btn-sm">السابق</button>`;
            }

            // أرقام الصفحات
            const startPage = Math.max(1, pagination.current_page - 2);
            const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

            if (startPage > 1) {
                html += `<button onclick="loadData(1)" class="btn btn-outline btn-sm">1</button>`;
                if (startPage > 2) html += '<span>...</span>';
            }

            for (let i = startPage; i <= endPage; i++) {
                const isActive = i === pagination.current_page;
                html += `<button onclick="loadData(${i})" class="btn ${isActive ? 'btn-primary' : 'btn-outline'} btn-sm">${i}</button>`;
            }

            if (endPage < pagination.total_pages) {
                if (endPage < pagination.total_pages - 1) html += '<span>...</span>';
                html += `<button onclick="loadData(${pagination.total_pages})" class="btn btn-outline btn-sm">${pagination.total_pages}</button>`;
            }

            // زر التالي
            if (pagination.has_next) {
                html += `<button onclick="loadData(${pagination.current_page + 1})" class="btn btn-outline btn-sm">التالي</button>`;
            }

            html += '</div>';
            html += `<div class="text-center mt-2 text-sm text-secondary">
                عرض ${pagination.current_page} من ${pagination.total_pages} 
                (${pagination.total_records.toLocaleString()} سجل إجمالي)
            </div>`;

            container.innerHTML = html;
        }

        function handleSearch(e) {
            searchQuery = e.target.value.trim();
            currentPage = 1;
            loadData();
        }

        function clearSearch() {
            document.getElementById('searchInput').value = '';
            searchQuery = '';
            currentPage = 1;
            loadData();
        }

        function handleSort(e) {
            const column = e.currentTarget.dataset.column;
            
            if (currentSort === column) {
                currentOrder = currentOrder === 'ASC' ? 'DESC' : 'ASC';
            } else {
                currentSort = column;
                currentOrder = 'ASC';
            }

            // تحديث أيقونات الترتيب
            document.querySelectorAll('.sort-btn i').forEach(icon => {
                icon.className = 'fas fa-sort';
            });

            const icon = e.currentTarget.querySelector('i');
            icon.className = currentOrder === 'ASC' ? 'fas fa-sort-up' : 'fas fa-sort-down';

            currentPage = 1;
            loadData();
        }

        async function exportData(format) {
            try {
                const params = new URLSearchParams({
                    table: tableName,
                    format: format,
                    q: searchQuery
                });

                const response = await fetch(`api/export.php?${params}`);
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${tableName}.${format}`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    showAlert('تم تصدير البيانات بنجاح', 'success');
                } else {
                    const result = await response.json();
                    showAlert(result.error || 'حدث خطأ أثناء التصدير', 'error');
                }
            } catch (error) {
                console.error('Export error:', error);
                showAlert('حدث خطأ أثناء التصدير', 'error');
            }
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>

    <style>
        .sort-btn {
            background: none;
            border: none;
            color: inherit;
            font: inherit;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            width: 100%;
            text-align: right;
        }

        .sort-btn:hover {
            color: var(--primary-color);
        }

        .sort-btn i {
            font-size: 0.8rem;
            opacity: 0.6;
        }

        .sort-btn:hover i {
            opacity: 1;
        }
    </style>
</body>
</html>
