<?php
/**
 * API حذف الملفات والبيانات
 * File and Data Deletion API
 */

require_once __DIR__ . '/../config/config.php';

// التأكد من أن الطلب POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendErrorResponse('طريقة الطلب غير صحيحة', 405);
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendErrorResponse('بيانات الطلب غير صحيحة');
}

// التحقق من CSRF Token
if (!isset($input['csrf_token']) || !verifyCSRFToken($input['csrf_token'])) {
    sendErrorResponse('رمز الأمان غير صحيح', 403);
}

// الحصول على المعاملات
$fileId = $input['file_id'] ?? null;
$tableName = $input['table_name'] ?? '';

// تنظيف المدخلات
$fileId = $fileId ? intval($fileId) : null;
$tableName = sanitizeInput($tableName);

if (!$fileId || empty($tableName)) {
    sendErrorResponse('معرف الملف واسم الجدول مطلوبان');
}

try {
    $db = getDB();
    
    // بدء معاملة قاعدة البيانات
    $db->beginTransaction();
    
    try {
        // التحقق من وجود الملف
        $fileInfo = $db->fetchOne("
            SELECT id, original_name, table_name 
            FROM imported_files 
            WHERE id = ? AND table_name = ?
        ", [$fileId, $tableName]);
        
        if (!$fileInfo) {
            throw new Exception('الملف المطلوب غير موجود');
        }
        
        // حذف الجدول إذا كان موجوداً
        if ($db->tableExists($tableName)) {
            $db->execute("DROP TABLE `$tableName`");
        }
        
        // حذف سجل الملف من قاعدة البيانات
        $deletedRows = $db->execute("DELETE FROM imported_files WHERE id = ?", [$fileId]);
        
        if ($deletedRows === 0) {
            throw new Exception('فشل في حذف سجل الملف');
        }
        
        // حذف سجلات النشاط المرتبطة
        $db->execute("DELETE FROM activity_log WHERE table_name = ?", [$tableName]);
        
        // تأكيد المعاملة
        $db->commit();
        
        // تسجيل النشاط
        logActivity('delete', null, "تم حذف الملف: {$fileInfo['original_name']} (الجدول: $tableName)");
        
        sendSuccessResponse([
            'deleted_file' => $fileInfo['original_name'],
            'deleted_table' => $tableName
        ], 'تم حذف الملف والبيانات بنجاح');
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Delete error: " . $e->getMessage());
    sendErrorResponse('حدث خطأ أثناء حذف الملف: ' . $e->getMessage());
}
?>
