<?php
/**
 * تشخيص مشاكل قاعدة البيانات
 * Database Issues Diagnosis
 */

echo "<h2>تشخيص مشاكل قاعدة البيانات</h2>";
echo "<style>
    .success { color: #059669; }
    .error { color: #dc2626; }
    .warning { color: #d97706; }
    .info { color: #2563eb; }
    pre { background: #f8fafc; padding: 15px; border-radius: 8px; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #e2e8f0; border-radius: 8px; }
</style>";

$db_config = [
    'host' => 'localhost',
    'name' => 'csv_manager',
    'user' => 'root',
    'pass' => ''
];

echo "<div class='section'>";
echo "<h3>1. فحص اتصال MySQL</h3>";
echo "<pre>";

try {
    $pdo = new PDO("mysql:host={$db_config['host']};charset=utf8mb4", $db_config['user'], $db_config['pass'], [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "<span class='success'>✅ الاتصال بخادم MySQL ناجح</span>\n";
    
    // فحص إصدار MySQL
    $version = $pdo->query("SELECT VERSION()")->fetchColumn();
    echo "<span class='info'>📋 إصدار MySQL: $version</span>\n";
    
} catch (PDOException $e) {
    echo "<span class='error'>❌ فشل الاتصال بخادم MySQL: " . $e->getMessage() . "</span>\n";
    echo "</pre></div>";
    exit;
}

echo "</pre></div>";

echo "<div class='section'>";
echo "<h3>2. فحص قاعدة البيانات</h3>";
echo "<pre>";

try {
    // فحص وجود قاعدة البيانات
    $databases = $pdo->query("SHOW DATABASES LIKE '{$db_config['name']}'")->fetchAll();
    
    if (empty($databases)) {
        echo "<span class='warning'>⚠️ قاعدة البيانات '{$db_config['name']}' غير موجودة</span>\n";
        echo "<span class='info'>💡 سيتم إنشاؤها تلقائياً</span>\n";
        
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$db_config['name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<span class='success'>✅ تم إنشاء قاعدة البيانات</span>\n";
    } else {
        echo "<span class='success'>✅ قاعدة البيانات '{$db_config['name']}' موجودة</span>\n";
    }
    
    // الاتصال بقاعدة البيانات
    $pdo->exec("USE `{$db_config['name']}`");
    
} catch (PDOException $e) {
    echo "<span class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</span>\n";
}

echo "</pre></div>";

echo "<div class='section'>";
echo "<h3>3. فحص الجداول</h3>";
echo "<pre>";

try {
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $required_tables = ['imported_files', 'system_settings', 'activity_log', 'user_sessions'];
    
    echo "<span class='info'>📋 الجداول الموجودة:</span>\n";
    if (empty($tables)) {
        echo "<span class='warning'>⚠️ لا توجد جداول</span>\n";
    } else {
        foreach ($tables as $table) {
            echo "  - $table\n";
        }
    }
    
    echo "\n<span class='info'>📋 الجداول المطلوبة:</span>\n";
    $missing_tables = array_diff($required_tables, $tables);
    
    foreach ($required_tables as $table) {
        if (in_array($table, $tables)) {
            echo "<span class='success'>  ✅ $table</span>\n";
        } else {
            echo "<span class='error'>  ❌ $table (مفقود)</span>\n";
        }
    }
    
    if (!empty($missing_tables)) {
        echo "\n<span class='warning'>⚠️ جداول مفقودة: " . implode(', ', $missing_tables) . "</span>\n";
    }
    
} catch (PDOException $e) {
    echo "<span class='error'>❌ خطأ في فحص الجداول: " . $e->getMessage() . "</span>\n";
}

echo "</pre></div>";

echo "<div class='section'>";
echo "<h3>4. فحص ملف setup.sql</h3>";
echo "<pre>";

$sql_file = 'database/setup.sql';
if (file_exists($sql_file)) {
    echo "<span class='success'>✅ ملف setup.sql موجود</span>\n";
    
    $content = file_get_contents($sql_file);
    $size = strlen($content);
    echo "<span class='info'>📋 حجم الملف: " . number_format($size) . " بايت</span>\n";
    
    // فحص محتوى الملف
    $lines = explode("\n", $content);
    $create_tables = 0;
    $insert_statements = 0;
    
    foreach ($lines as $line) {
        if (stripos($line, 'CREATE TABLE') !== false) {
            $create_tables++;
        }
        if (stripos($line, 'INSERT INTO') !== false) {
            $insert_statements++;
        }
    }
    
    echo "<span class='info'>📋 عدد الأسطر: " . count($lines) . "</span>\n";
    echo "<span class='info'>📋 استعلامات CREATE TABLE: $create_tables</span>\n";
    echo "<span class='info'>📋 استعلامات INSERT: $insert_statements</span>\n";
    
    if ($create_tables < 4) {
        echo "<span class='warning'>⚠️ عدد استعلامات CREATE TABLE أقل من المتوقع</span>\n";
    }
    
} else {
    echo "<span class='error'>❌ ملف setup.sql غير موجود في: $sql_file</span>\n";
}

echo "</pre></div>";

echo "<div class='section'>";
echo "<h3>5. فحص ملفات الإعدادات</h3>";
echo "<pre>";

$config_files = [
    'config/database.php' => 'ملف إعدادات قاعدة البيانات',
    'config/config.php' => 'ملف الإعدادات العامة'
];

foreach ($config_files as $file => $description) {
    if (file_exists($file)) {
        echo "<span class='success'>✅ $description موجود</span>\n";
        
        if (is_readable($file)) {
            echo "<span class='info'>  📖 قابل للقراءة</span>\n";
        } else {
            echo "<span class='error'>  ❌ غير قابل للقراءة</span>\n";
        }
        
        if (is_writable($file)) {
            echo "<span class='info'>  ✏️ قابل للكتابة</span>\n";
        } else {
            echo "<span class='warning'>  ⚠️ غير قابل للكتابة</span>\n";
        }
        
    } else {
        echo "<span class='error'>❌ $description غير موجود: $file</span>\n";
    }
}

echo "</pre></div>";

echo "<div class='section'>";
echo "<h3>6. اختبار تنفيذ SQL</h3>";
echo "<pre>";

if (file_exists($sql_file)) {
    try {
        $content = file_get_contents($sql_file);
        
        // تنظيف المحتوى
        $content = preg_replace('/--.*$/m', '', $content);
        $content = preg_replace('/\/\*.*?\*\//s', '', $content);
        
        // تقسيم الاستعلامات
        $statements = array_filter(explode(';', $content), function($stmt) {
            return !empty(trim($stmt)) && strlen(trim($stmt)) > 10;
        });
        
        echo "<span class='info'>📋 عدد الاستعلامات المستخرجة: " . count($statements) . "</span>\n\n";
        
        $executed = 0;
        $failed = 0;
        
        foreach ($statements as $i => $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);
                    $executed++;
                    
                    // عرض نوع الاستعلام
                    if (stripos($statement, 'CREATE TABLE') !== false) {
                        preg_match('/CREATE TABLE.*?`?(\w+)`?\s*\(/i', $statement, $matches);
                        if (isset($matches[1])) {
                            echo "<span class='success'>✅ تم إنشاء جدول: {$matches[1]}</span>\n";
                        }
                    } elseif (stripos($statement, 'INSERT INTO') !== false) {
                        echo "<span class='success'>✅ تم إدراج بيانات</span>\n";
                    }
                    
                } catch (PDOException $e) {
                    $failed++;
                    echo "<span class='error'>❌ فشل الاستعلام " . ($i + 1) . ": " . $e->getMessage() . "</span>\n";
                    echo "<span class='info'>   SQL: " . substr($statement, 0, 100) . "...</span>\n";
                }
            }
        }
        
        echo "\n<span class='info'>📊 النتيجة: تم تنفيذ $executed استعلام، فشل $failed استعلام</span>\n";
        
    } catch (Exception $e) {
        echo "<span class='error'>❌ خطأ في معالجة ملف SQL: " . $e->getMessage() . "</span>\n";
    }
}

echo "</pre></div>";

echo "<div class='section'>";
echo "<h3>7. التحقق النهائي</h3>";
echo "<pre>";

try {
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $required_tables = ['imported_files', 'system_settings', 'activity_log', 'user_sessions'];
    $missing_tables = array_diff($required_tables, $tables);
    
    if (empty($missing_tables)) {
        echo "<span class='success'>🎉 جميع الجداول المطلوبة موجودة!</span>\n";
        
        // اختبار البيانات
        foreach ($tables as $table) {
            try {
                $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
                echo "<span class='info'>📊 جدول $table: $count سجل</span>\n";
            } catch (PDOException $e) {
                echo "<span class='error'>❌ خطأ في جدول $table: " . $e->getMessage() . "</span>\n";
            }
        }
        
    } else {
        echo "<span class='error'>❌ لا تزال هناك جداول مفقودة: " . implode(', ', $missing_tables) . "</span>\n";
    }
    
} catch (PDOException $e) {
    echo "<span class='error'>❌ خطأ في التحقق النهائي: " . $e->getMessage() . "</span>\n";
}

echo "</pre></div>";

// أزرار الإجراءات
echo "<div class='section'>";
echo "<h3>الإجراءات المتاحة</h3>";
echo '<a href="quick_setup.php" style="background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">إعداد سريع</a> ';
echo '<a href="database_setup.php" style="background: #2563eb; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">إعداد متقدم</a> ';
echo '<a href="test_system.php" style="background: #d97706; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">اختبار النظام</a> ';
echo '<a href="index.php" style="background: #6b7280; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">الصفحة الرئيسية</a>';
echo "</div>";
?>
