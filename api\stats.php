<?php
/**
 * API الإحصائيات
 * Statistics API
 */

require_once __DIR__ . '/../config/config.php';

// التأكد من أن الطلب GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendErrorResponse('طريقة الطلب غير صحيحة', 405);
}

try {
    $db = getDB();
    
    // إجمالي الملفات
    $totalFiles = $db->fetchOne("
        SELECT COUNT(*) as count 
        FROM imported_files 
        WHERE status = 'completed'
    ")['count'] ?? 0;
    
    // إجمالي السجلات
    $totalRecords = $db->fetchOne("
        SELECT SUM(total_rows) as total 
        FROM imported_files 
        WHERE status = 'completed'
    ")['total'] ?? 0;
    
    // إجمالي حجم الملفات
    $totalSize = $db->fetchOne("
        SELECT SUM(file_size) as total 
        FROM imported_files 
        WHERE status = 'completed'
    ")['total'] ?? 0;
    
    // آخر ملف تم رفعه
    $lastUpload = $db->fetchOne("
        SELECT original_name, upload_date 
        FROM imported_files 
        WHERE status = 'completed' 
        ORDER BY upload_date DESC 
        LIMIT 1
    ");
    
    // إحصائيات الأنشطة الأخيرة
    $recentActivity = $db->fetchAll("
        SELECT action_type, COUNT(*) as count
        FROM activity_log 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY action_type
        ORDER BY count DESC
    ");
    
    // الملفات الأكثر استخداماً
    $popularTables = $db->fetchAll("
        SELECT 
            f.original_name,
            f.table_name,
            f.total_rows,
            COUNT(a.id) as search_count
        FROM imported_files f
        LEFT JOIN activity_log a ON f.table_name = a.table_name AND a.action_type = 'search'
        WHERE f.status = 'completed'
        GROUP BY f.id
        ORDER BY search_count DESC, f.upload_date DESC
        LIMIT 5
    ");
    
    // إحصائيات شهرية
    $monthlyStats = $db->fetchAll("
        SELECT 
            DATE_FORMAT(upload_date, '%Y-%m') as month,
            COUNT(*) as files_count,
            SUM(total_rows) as records_count,
            SUM(file_size) as total_size
        FROM imported_files 
        WHERE status = 'completed' 
        AND upload_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(upload_date, '%Y-%m')
        ORDER BY month DESC
    ");
    
    // معلومات قاعدة البيانات
    $dbInfo = $db->fetchOne("
        SELECT 
            COUNT(*) as table_count
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
        AND table_name NOT IN ('imported_files', 'system_settings', 'activity_log', 'user_sessions')
    ");
    
    $dbSize = $db->fetchOne("
        SELECT 
            ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
    ");
    
    // إرسال النتيجة
    sendSuccessResponse([
        'total_files' => (int)$totalFiles,
        'total_records' => (int)$totalRecords,
        'total_size' => (int)$totalSize,
        'last_upload' => $lastUpload ? $lastUpload['original_name'] . ' (' . date('Y-m-d H:i', strtotime($lastUpload['upload_date'])) . ')' : null,
        'recent_activity' => $recentActivity,
        'popular_tables' => $popularTables,
        'monthly_stats' => $monthlyStats,
        'database_info' => [
            'table_count' => (int)($dbInfo['table_count'] ?? 0),
            'size_mb' => (float)($dbSize['size_mb'] ?? 0)
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Stats error: " . $e->getMessage());
    sendErrorResponse('حدث خطأ أثناء تحميل الإحصائيات: ' . $e->getMessage());
}
?>
