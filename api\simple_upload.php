<?php
/**
 * معالج رفع بسيط جداً
 * Ultra Simple Upload Handler
 */

// منع أي مخرجات
ob_start();
error_reporting(0);
ini_set('display_errors', 0);

// دالة JSON بسيطة
function json_response($data) {
    ob_clean();
    header('Content-Type: application/json');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// دالة خطأ
function json_error($message) {
    json_response(['success' => false, 'error' => $message]);
}

// دالة نجاح
function json_success($data, $message = 'تم بنجاح') {
    json_response(['success' => true, 'message' => $message, 'data' => $data]);
}

try {
    // التحقق من الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        json_error('طريقة طلب خاطئة');
    }

    // التحقق من الملف
    if (!isset($_FILES['csv_file'])) {
        json_error('لم يتم رفع ملف');
    }

    $file = $_FILES['csv_file'];
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        json_error('خطأ في رفع الملف: ' . $file['error']);
    }

    // التحقق من النوع
    $ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($ext, ['csv', 'txt'])) {
        json_error('نوع الملف غير مدعوم. استخدم CSV أو TXT فقط.');
    }

    // التحقق من الحجم (250MB)
    if ($file['size'] > 250 * 1024 * 1024) {
        json_error('الملف كبير جداً. الحد الأقصى 250 ميجابايت.');
    }

    // إنشاء مجلد uploads
    $uploadsDir = dirname(__DIR__) . '/uploads';
    if (!is_dir($uploadsDir)) {
        mkdir($uploadsDir, 0755, true);
    }

    // نقل الملف
    $fileName = time() . '_' . basename($file['name']);
    $filePath = $uploadsDir . '/' . $fileName;
    
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        json_error('فشل في حفظ الملف');
    }

    // قراءة الملف
    $content = file_get_contents($filePath);
    if (!$content) {
        unlink($filePath);
        json_error('فشل في قراءة الملف');
    }

    // تحليل CSV
    $delimiter = $_POST['delimiter'] ?? ',';
    $lines = explode("\n", trim($content));
    
    if (count($lines) < 2) {
        unlink($filePath);
        json_error('الملف يجب أن يحتوي على رأس أعمدة وسطر واحد على الأقل');
    }

    // الحصول على الأعمدة
    $headers = str_getcsv($lines[0], $delimiter);
    $headers = array_map('trim', $headers);
    
    if (empty($headers)) {
        unlink($filePath);
        json_error('لا يمكن قراءة رأس الأعمدة');
    }

    // تنظيف أسماء الأعمدة
    $cleanHeaders = [];
    foreach ($headers as $i => $header) {
        $clean = preg_replace('/[^a-zA-Z0-9_\x{0600}-\x{06FF}]/u', '_', $header);
        $clean = trim($clean, '_');
        if (empty($clean)) {
            $clean = 'column_' . ($i + 1);
        }
        $cleanHeaders[] = $clean;
    }

    // اتصال قاعدة البيانات بسيط
    $host = 'localhost';
    $dbname = 'csv_manager';
    $username = 'root';
    $password = '';

    try {
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
    } catch (PDOException $e) {
        unlink($filePath);
        json_error('فشل الاتصال بقاعدة البيانات: ' . $e->getMessage());
    }

    // إنشاء اسم جدول
    $tableName = 'csv_' . time() . '_' . rand(1000, 9999);

    // إنشاء الجدول
    $createSQL = "CREATE TABLE `$tableName` (id INT AUTO_INCREMENT PRIMARY KEY";
    foreach ($cleanHeaders as $header) {
        $createSQL .= ", `$header` TEXT";
    }
    $createSQL .= ", created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP)";

    try {
        $pdo->exec($createSQL);
    } catch (PDOException $e) {
        unlink($filePath);
        json_error('فشل في إنشاء الجدول: ' . $e->getMessage());
    }

    // إدراج البيانات
    $insertedRows = 0;
    $placeholders = str_repeat('?,', count($cleanHeaders));
    $placeholders = rtrim($placeholders, ',');
    $insertSQL = "INSERT INTO `$tableName` (`" . implode('`, `', $cleanHeaders) . "`) VALUES ($placeholders)";
    
    $stmt = $pdo->prepare($insertSQL);

    for ($i = 1; $i < count($lines); $i++) {
        $line = trim($lines[$i]);
        if (empty($line)) continue;
        
        $row = str_getcsv($line, $delimiter);
        
        // ملء الصفوف الناقصة
        while (count($row) < count($cleanHeaders)) {
            $row[] = '';
        }
        
        // قطع الصفوف الزائدة
        $row = array_slice($row, 0, count($cleanHeaders));
        
        try {
            $stmt->execute($row);
            $insertedRows++;
        } catch (PDOException $e) {
            // تجاهل الأخطاء في الصفوف الفردية
        }
    }

    // تسجيل في imported_files
    try {
        $fileSQL = "INSERT INTO imported_files (file_name, original_name, table_name, file_size, total_rows, status) VALUES (?, ?, ?, ?, ?, 'completed')";
        $pdo->prepare($fileSQL)->execute([
            $fileName,
            $file['name'],
            $tableName,
            $file['size'],
            $insertedRows
        ]);
    } catch (PDOException $e) {
        // إذا فشل التسجيل، لا نحذف الجدول لأن البيانات موجودة
    }

    // النجاح
    json_success([
        'table_name' => $tableName,
        'file_name' => $file['name'],
        'total_rows' => $insertedRows,
        'columns' => $cleanHeaders
    ], "تم استيراد $insertedRows سجل بنجاح");

} catch (Exception $e) {
    json_error('خطأ: ' . $e->getMessage());
} catch (Error $e) {
    json_error('خطأ نظام: ' . $e->getMessage());
}
?>
