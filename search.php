<?php
/**
 * صفحة البحث المتقدم
 * Advanced Search Page
 */

require_once 'config/config.php';

// الحصول على قائمة الجداول المتاحة
try {
    $db = getDB();
    $availableTables = $db->fetchAll("
        SELECT id, original_name, table_name, total_rows, upload_date 
        FROM imported_files 
        WHERE status = 'completed' 
        ORDER BY upload_date DESC
    ");
} catch (Exception $e) {
    $availableTables = [];
}

$pageTitle = 'البحث المتقدم';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <title><?php echo $pageTitle . ' - ' . SITE_TITLE; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-file-csv"></i>
                    <?php echo SITE_TITLE; ?>
                </div>
                <nav class="nav">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                    <a href="files.php" class="nav-link">
                        <i class="fas fa-folder"></i>
                        الملفات
                    </a>
                    <a href="search.php" class="nav-link active">
                        <i class="fas fa-search"></i>
                        البحث
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="container">
            <!-- عنوان الصفحة -->
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">
                        <i class="fas fa-search"></i>
                        البحث المتقدم في البيانات
                    </h1>
                    <p class="card-description">
                        ابحث في جميع الملفات المستوردة أو اختر ملف محدد للبحث فيه.
                    </p>
                </div>
            </div>

            <?php if (empty($availableTables)): ?>
            <!-- رسالة عدم وجود ملفات -->
            <div class="card">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>لا توجد ملفات للبحث فيها</strong><br>
                        يرجى <a href="index.php">رفع ملف CSV</a> أولاً لتتمكن من البحث في البيانات.
                    </div>
                </div>
            </div>
            <?php else: ?>

            <!-- نموذج البحث -->
            <div class="card">
                <form id="searchForm">
                    <div class="grid grid-2">
                        <div class="form-group">
                            <label for="searchTable" class="form-label">اختر الملف للبحث فيه</label>
                            <select id="searchTable" name="table" class="form-select" required>
                                <option value="">-- اختر ملف --</option>
                                <?php foreach ($availableTables as $table): ?>
                                <option value="<?php echo htmlspecialchars($table['table_name']); ?>" 
                                        data-rows="<?php echo $table['total_rows']; ?>">
                                    <?php echo htmlspecialchars($table['original_name']); ?> 
                                    (<?php echo number_format($table['total_rows']); ?> سجل)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="searchQuery" class="form-label">كلمة البحث</label>
                            <input type="text" id="searchQuery" name="q" class="form-input" 
                                   placeholder="ادخل كلمة البحث...">
                        </div>
                    </div>

                    <div class="grid grid-3">
                        <div class="form-group">
                            <label for="sortBy" class="form-label">ترتيب حسب</label>
                            <select id="sortBy" name="sort" class="form-select">
                                <option value="">الافتراضي</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="sortOrder" class="form-label">نوع الترتيب</label>
                            <select id="sortOrder" name="order" class="form-select">
                                <option value="ASC">تصاعدي</option>
                                <option value="DESC">تنازلي</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="limitResults" class="form-label">عدد النتائج</label>
                            <select id="limitResults" name="limit" class="form-select">
                                <option value="25">25</option>
                                <option value="50" selected>50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <button type="button" id="clearSearch" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            مسح
                        </button>
                    </div>
                </form>
            </div>

            <!-- معلومات الجدول المحدد -->
            <div id="tableInfo" class="card hidden">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle"></i>
                        معلومات الملف
                    </h3>
                </div>
                <div id="tableInfoContent"></div>
            </div>

            <!-- نتائج البحث -->
            <div id="searchResults" class="card hidden">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-list"></i>
                        نتائج البحث
                    </h3>
                    <div id="searchStats"></div>
                </div>

                <!-- شريط الأدوات -->
                <div id="resultsToolbar" class="mb-3" style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                    <div>
                        <button id="exportCSV" class="btn btn-outline btn-sm">
                            <i class="fas fa-download"></i>
                            تصدير CSV
                        </button>
                        <button id="exportExcel" class="btn btn-outline btn-sm">
                            <i class="fas fa-file-excel"></i>
                            تصدير Excel
                        </button>
                    </div>
                    <div id="paginationTop"></div>
                </div>

                <!-- جدول النتائج -->
                <div class="table-container">
                    <table id="resultsTable" class="table">
                        <thead id="resultsTableHead"></thead>
                        <tbody id="resultsTableBody"></tbody>
                    </table>
                </div>

                <!-- التنقل بين الصفحات -->
                <div id="paginationBottom" class="mt-3 text-center"></div>
            </div>

            <?php endif; ?>
        </div>
    </main>

    <!-- الفوتر -->
    <footer class="footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_TITLE; ?>. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        // متغيرات البحث
        let currentSearchData = null;
        let currentTableColumns = [];

        // تهيئة صفحة البحث
        document.addEventListener('DOMContentLoaded', function() {
            initializeSearchPage();
        });

        function initializeSearchPage() {
            const searchForm = document.getElementById('searchForm');
            const searchTable = document.getElementById('searchTable');
            const clearButton = document.getElementById('clearSearch');

            // تغيير الجدول
            searchTable.addEventListener('change', handleTableChange);
            
            // إرسال النموذج
            searchForm.addEventListener('submit', handleSearchSubmit);
            
            // مسح البحث
            clearButton.addEventListener('click', clearSearch);

            // البحث الفوري
            const searchQuery = document.getElementById('searchQuery');
            searchQuery.addEventListener('input', debounce(handleInstantSearch, 500));
        }

        async function handleTableChange(e) {
            const tableName = e.target.value;
            const tableInfo = document.getElementById('tableInfo');
            const sortBy = document.getElementById('sortBy');

            if (!tableName) {
                tableInfo.classList.add('hidden');
                sortBy.innerHTML = '<option value="">الافتراضي</option>';
                return;
            }

            try {
                // الحصول على معلومات الجدول
                const response = await fetch(`api/table-info.php?table=${encodeURIComponent(tableName)}`);
                const result = await response.json();

                if (result.success) {
                    displayTableInfo(result.data);
                    updateSortOptions(result.data.columns);
                    currentTableColumns = result.data.columns;
                } else {
                    showAlert(result.error, 'error');
                }
            } catch (error) {
                console.error('Error loading table info:', error);
                showAlert('حدث خطأ أثناء تحميل معلومات الجدول', 'error');
            }
        }

        function displayTableInfo(data) {
            const tableInfo = document.getElementById('tableInfo');
            const content = document.getElementById('tableInfoContent');

            content.innerHTML = `
                <div class="grid grid-3">
                    <div>
                        <strong>عدد الأعمدة:</strong> ${data.columns.length}
                    </div>
                    <div>
                        <strong>عدد السجلات:</strong> ${data.total_rows.toLocaleString()}
                    </div>
                    <div>
                        <strong>تاريخ الرفع:</strong> ${new Date(data.upload_date).toLocaleDateString('ar')}
                    </div>
                </div>
                <div class="mt-3">
                    <strong>الأعمدة:</strong>
                    <div class="mt-2" style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                        ${data.columns.map(col => `<span class="badge">${col}</span>`).join('')}
                    </div>
                </div>
            `;

            tableInfo.classList.remove('hidden');
        }

        function updateSortOptions(columns) {
            const sortBy = document.getElementById('sortBy');
            sortBy.innerHTML = '<option value="">الافتراضي</option>';
            
            columns.forEach(column => {
                if (column !== 'id' && column !== 'created_at' && column !== 'updated_at') {
                    sortBy.innerHTML += `<option value="${column}">${column}</option>`;
                }
            });
        }

        async function handleSearchSubmit(e) {
            e.preventDefault();
            await performSearch();
        }

        async function handleInstantSearch() {
            const tableName = document.getElementById('searchTable').value;
            const query = document.getElementById('searchQuery').value.trim();
            
            if (tableName && query.length >= 2) {
                await performSearch();
            }
        }

        async function performSearch(page = 1) {
            const formData = new FormData(document.getElementById('searchForm'));
            formData.append('page', page);

            try {
                const params = new URLSearchParams(formData);
                const response = await fetch(`api/search.php?${params}`);
                const result = await response.json();

                if (result.success) {
                    displaySearchResults(result.data);
                    currentSearchData = result.data;
                } else {
                    showAlert(result.error, 'error');
                }
            } catch (error) {
                console.error('Search error:', error);
                showAlert('حدث خطأ أثناء البحث', 'error');
            }
        }

        function displaySearchResults(data) {
            const resultsCard = document.getElementById('searchResults');
            const statsDiv = document.getElementById('searchStats');
            const tableHead = document.getElementById('resultsTableHead');
            const tableBody = document.getElementById('resultsTableBody');

            // إظهار النتائج
            resultsCard.classList.remove('hidden');

            // عرض الإحصائيات
            statsDiv.innerHTML = `
                <small class="text-secondary">
                    عرض ${data.data.length} من أصل ${data.pagination.total_records.toLocaleString()} سجل
                    ${data.search.query ? `- البحث عن: "${data.search.query}"` : ''}
                </small>
            `;

            // إنشاء رأس الجدول
            if (data.data.length > 0) {
                const headers = Object.keys(data.data[0]);
                tableHead.innerHTML = `
                    <tr>
                        ${headers.map(header => `<th>${header}</th>`).join('')}
                    </tr>
                `;

                // إنشاء صفوف الجدول
                tableBody.innerHTML = data.data.map(row => `
                    <tr>
                        ${Object.values(row).map(value => `<td>${escapeHtml(value || '')}</td>`).join('')}
                    </tr>
                `).join('');
            } else {
                tableHead.innerHTML = '';
                tableBody.innerHTML = '<tr><td colspan="100%" class="text-center">لا توجد نتائج</td></tr>';
            }

            // إنشاء التنقل بين الصفحات
            createPagination(data.pagination);
        }

        function createPagination(pagination) {
            const topPagination = document.getElementById('paginationTop');
            const bottomPagination = document.getElementById('paginationBottom');
            
            let paginationHTML = '';
            
            if (pagination.total_pages > 1) {
                paginationHTML = `
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        ${pagination.has_prev ? `<button onclick="performSearch(${pagination.current_page - 1})" class="btn btn-outline btn-sm">السابق</button>` : ''}
                        <span>صفحة ${pagination.current_page} من ${pagination.total_pages}</span>
                        ${pagination.has_next ? `<button onclick="performSearch(${pagination.current_page + 1})" class="btn btn-outline btn-sm">التالي</button>` : ''}
                    </div>
                `;
            }
            
            topPagination.innerHTML = paginationHTML;
            bottomPagination.innerHTML = paginationHTML;
        }

        function clearSearch() {
            document.getElementById('searchForm').reset();
            document.getElementById('tableInfo').classList.add('hidden');
            document.getElementById('searchResults').classList.add('hidden');
            currentSearchData = null;
            currentTableColumns = [];
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    </script>

    <style>
        .badge {
            background-color: var(--primary-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
        }
    </style>
</body>
</html>
