;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; إعدادات PHP المطلوبة لمدير ملفات CSV
; Required PHP Settings for CSV Manager
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

; إعدادات رفع الملفات - File Upload Settings
upload_max_filesize = 250M
post_max_size = 250M
max_file_uploads = 20

; إعدادات الذاكرة والوقت - Memory and Time Settings
memory_limit = 512M
max_execution_time = 600
max_input_time = 600

; إعدادات الجلسات - Session Settings
session.gc_maxlifetime = 3600
session.cookie_lifetime = 0

; إعدادات قاعدة البيانات - Database Settings
default_socket_timeout = 60

; إعدادات الأمان - Security Settings
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; إعدادات الأخطاء - Error Settings
display_errors = Off
log_errors = On
error_log = /path/to/your/logs/php_errors.log

; إعدادات الترميز - Encoding Settings
default_charset = "UTF-8"
mbstring.internal_encoding = UTF-8
mbstring.http_output = UTF-8

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; تعليمات التطبيق - Application Instructions
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

; لتطبيق هذه الإعدادات:
; To apply these settings:

; 1. انسخ هذه الإعدادات إلى ملف php.ini الخاص بك
;    Copy these settings to your php.ini file

; 2. أو أنشئ ملف .htaccess في مجلد المشروع مع:
;    Or create .htaccess file in project folder with:
;    php_value upload_max_filesize 250M
;    php_value post_max_size 250M
;    php_value memory_limit 512M
;    php_value max_execution_time 600

; 3. أعد تشغيل خادم الويب
;    Restart web server

; 4. تحقق من الإعدادات باستخدام phpinfo()
;    Check settings using phpinfo()

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; ملاحظات مهمة - Important Notes
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

; - تأكد من أن مساحة القرص كافية لتخزين الملفات الكبيرة
;   Make sure disk space is sufficient for large files

; - راقب استخدام الذاكرة عند معالجة ملفات كبيرة
;   Monitor memory usage when processing large files

; - استخدم قاعدة بيانات محسنة للأداء مع الملفات الكبيرة
;   Use optimized database for performance with large files

; - فكر في استخدام معالجة مجمعة للملفات الكبيرة جداً
;   Consider batch processing for very large files
