<?php
/**
 * معالج رفع ملفات CSV - نسخة محسنة
 * CSV Upload Handler - Fixed Version
 */

// منع أي مخرجات قبل JSON
ob_start();

// تعطيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// دالة إرسال JSON محسنة
function sendJSON($data, $statusCode = 200) {
    // تنظيف أي مخرجات سابقة
    if (ob_get_level()) {
        ob_clean();
    }
    
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');
    
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

// دالة إرسال خطأ
function sendError($message, $statusCode = 400) {
    sendJSON([
        'success' => false,
        'error' => $message
    ], $statusCode);
}

// دالة إرسال نجاح
function sendSuccess($data = [], $message = 'تم بنجاح') {
    sendJSON([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
}

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendError('طريقة الطلب غير صحيحة', 405);
    }
    
    // التحقق من وجود الملف
    if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
        sendError('لم يتم رفع ملف أو حدث خطأ في الرفع');
    }
    
    $file = $_FILES['csv_file'];
    
    // التحقق من نوع الملف
    $allowedTypes = ['text/csv', 'application/csv', 'text/plain'];
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file['type'], $allowedTypes) && !in_array($fileExtension, ['csv', 'txt'])) {
        sendError('نوع الملف غير مدعوم. يرجى اختيار ملف CSV أو TXT.');
    }
    
    // التحقق من حجم الملف (250 MB)
    if ($file['size'] > 250 * 1024 * 1024) {
        sendError('حجم الملف كبير جداً. الحد الأقصى المسموح 250 ميجابايت.');
    }
    
    // التحقق من وجود مجلد uploads
    $uploadsDir = __DIR__ . '/../uploads';
    if (!is_dir($uploadsDir)) {
        if (!mkdir($uploadsDir, 0755, true)) {
            sendError('فشل في إنشاء مجلد الرفع');
        }
    }
    
    // إنشاء اسم ملف فريد
    $fileName = time() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $file['name']);
    $filePath = $uploadsDir . '/' . $fileName;
    
    // نقل الملف
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        sendError('فشل في حفظ الملف');
    }
    
    // قراءة محتوى الملف
    $content = file_get_contents($filePath);
    if ($content === false) {
        unlink($filePath);
        sendError('فشل في قراءة محتوى الملف');
    }
    
    // تحويل الترميز إذا لزم الأمر
    $encoding = $_POST['encoding'] ?? 'UTF-8';
    if ($encoding !== 'UTF-8') {
        $content = mb_convert_encoding($content, 'UTF-8', $encoding);
    }
    
    // تحليل CSV
    $delimiter = $_POST['delimiter'] ?? ',';
    $lines = str_getcsv($content, "\n");
    
    if (empty($lines)) {
        unlink($filePath);
        sendError('الملف فارغ أو لا يحتوي على بيانات صحيحة');
    }
    
    // الحصول على رأس الأعمدة
    $headers = str_getcsv($lines[0], $delimiter);
    $headers = array_map('trim', $headers);
    
    if (empty($headers)) {
        unlink($filePath);
        sendError('لا يمكن قراءة رأس الأعمدة');
    }
    
    // تنظيف أسماء الأعمدة
    $cleanHeaders = [];
    foreach ($headers as $header) {
        $clean = preg_replace('/[^a-zA-Z0-9_\u0600-\u06FF]/', '_', $header);
        $clean = trim($clean, '_');
        if (empty($clean)) {
            $clean = 'column_' . (count($cleanHeaders) + 1);
        }
        $cleanHeaders[] = $clean;
    }
    
    // إنشاء اسم جدول فريد
    $tableName = 'csv_' . time() . '_' . rand(1000, 9999);
    
    // محاولة الاتصال بقاعدة البيانات
    try {
        require_once __DIR__ . '/../config/config.php';
        $db = getDB();
    } catch (Exception $e) {
        unlink($filePath);
        sendError('فشل في الاتصال بقاعدة البيانات: ' . $e->getMessage());
    }
    
    // إنشاء الجدول
    $createSQL = "CREATE TABLE `$tableName` (\n";
    $createSQL .= "id INT AUTO_INCREMENT PRIMARY KEY,\n";
    
    foreach ($cleanHeaders as $header) {
        $createSQL .= "`$header` TEXT,\n";
    }
    
    $createSQL .= "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n";
    $createSQL .= "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n";
    $createSQL .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    try {
        $db->execute($createSQL);
    } catch (Exception $e) {
        unlink($filePath);
        sendError('فشل في إنشاء الجدول: ' . $e->getMessage());
    }
    
    // إدراج البيانات
    $insertedRows = 0;
    $errors = [];
    
    for ($i = 1; $i < count($lines); $i++) {
        $row = str_getcsv($lines[$i], $delimiter);
        
        if (count($row) === count($cleanHeaders)) {
            try {
                $placeholders = str_repeat('?,', count($cleanHeaders));
                $placeholders = rtrim($placeholders, ',');
                
                $insertSQL = "INSERT INTO `$tableName` (`" . implode('`, `', $cleanHeaders) . "`) VALUES ($placeholders)";
                $db->execute($insertSQL, $row);
                $insertedRows++;
            } catch (Exception $e) {
                $errors[] = "خطأ في السطر $i: " . $e->getMessage();
            }
        }
    }
    
    // تسجيل الملف في قاعدة البيانات
    try {
        $insertFileSQL = "INSERT INTO imported_files (file_name, original_name, table_name, file_size, total_rows, columns_info, status) VALUES (?, ?, ?, ?, ?, ?, ?)";
        $db->execute($insertFileSQL, [
            $fileName,
            $file['name'],
            $tableName,
            $file['size'],
            $insertedRows,
            json_encode($cleanHeaders),
            'completed'
        ]);
    } catch (Exception $e) {
        // إذا فشل تسجيل الملف، احذف الجدول
        $db->execute("DROP TABLE IF EXISTS `$tableName`");
        unlink($filePath);
        sendError('فشل في تسجيل الملف: ' . $e->getMessage());
    }
    
    // إرسال النتيجة
    sendSuccess([
        'table_name' => $tableName,
        'file_name' => $file['name'],
        'total_rows' => $insertedRows,
        'columns' => $cleanHeaders,
        'errors' => $errors
    ], "تم استيراد $insertedRows سجل بنجاح");
    
} catch (Exception $e) {
    error_log("Upload error: " . $e->getMessage());
    sendError('حدث خطأ غير متوقع: ' . $e->getMessage());
}
?>
