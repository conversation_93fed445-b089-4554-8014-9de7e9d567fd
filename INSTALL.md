# دليل التثبيت السريع - مدير ملفات CSV

## خطوات التثبيت السريعة

### 1. متطلبات النظام
- XAMPP أو WAMP أو LAMP
- PHP 7.4+
- MySQL 5.7+

### 2. التثبيت

#### أ. نسخ الملفات
```bash
# انسخ المجلد إلى htdocs
cp -r CDCO/ C:/xampp/htdocs/
```

#### ب. إعداد قاعدة البيانات
1. افتح phpMyAdmin: `http://localhost/phpmyadmin`
2. أنشئ قاعدة بيانات جديدة باسم `csv_manager`
3. استورد ملف `database/setup.sql`

أو استخدم سطر الأوامر:
```sql
mysql -u root -p
CREATE DATABASE csv_manager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE csv_manager;
SOURCE database/setup.sql;
```

#### ج. تكوين الاتصال
عدّل `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'csv_manager');
define('DB_USER', 'root');
define('DB_PASS', ''); // ضع كلمة المرور هنا
```

### 3. الاختبار
1. افتح المتصفح: `http://localhost/CDCO`
2. جرب رفع ملف `test.csv` المرفق
3. تأكد من عمل جميع الوظائف

### 4. استكشاف الأخطاء

#### خطأ في قاعدة البيانات
```
خطأ في الاتصال بقاعدة البيانات
```
**الحل**: تأكد من تشغيل MySQL وصحة إعدادات الاتصال

#### خطأ في رفع الملفات
```
فشل في رفع الملف
```
**الحل**: تحقق من صلاحيات مجلد `uploads/` وأن حجم الملف أقل من 250 ميجابايت
```bash
chmod 755 uploads/
chmod 755 logs/
```

#### مشاكل الترميز العربي
**الحل**: تأكد من:
- ترميز قاعدة البيانات: `utf8mb4_unicode_ci`
- ترميز الملفات: UTF-8
- إعدادات المتصفح

### 5. الإعدادات الإضافية

#### زيادة حد رفع الملفات (اختياري)
النظام يدعم حالياً 250 ميجابايت. لرفع الحد أكثر، عدّل `php.ini`:
```ini
upload_max_filesize = 500M
post_max_size = 500M
max_execution_time = 1200
memory_limit = 1024M
```

#### تفعيل السجلات
```php
// في config/config.php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
```

## اختبار سريع

### 1. رفع ملف تجريبي
- استخدم `test.csv` المرفق
- يحتوي على بيانات مأموريات سعودية
- 10 سجلات مع 5 أعمدة

### 2. اختبار الوظائف
- ✅ رفع الملف
- ✅ عرض البيانات
- ✅ البحث الفوري
- ✅ التصدير
- ✅ الحذف

### 3. اختبار الأداء
- رفع ملف كبير (>1000 سجل)
- اختبار البحث السريع
- اختبار التصدير

## نصائح مهمة

### الأمان
- غيّر كلمة مرور قاعدة البيانات
- استخدم HTTPS في الإنتاج
- راجع ملفات السجلات دورياً

### الأداء
- استخدم فهرسة للجداول الكبيرة
- قم بتنظيف الملفات القديمة
- راقب استخدام الذاكرة

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u root -p csv_manager > backup.sql

# نسخ احتياطي للملفات
tar -czf backup.tar.gz uploads/ logs/
```

## الدعم

إذا واجهت مشاكل:
1. راجع `logs/error.log`
2. تحقق من سجل Apache/MySQL
3. تأكد من المتطلبات
4. جرب الملف التجريبي أولاً

---
**وقت التثبيت المتوقع**: 10-15 دقيقة  
**مستوى الصعوبة**: مبتدئ إلى متوسط
