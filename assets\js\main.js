/**
 * الملف الرئيسي للجافا سكريبت
 * Main JavaScript File
 */

// متغيرات عامة
let currentTable = null;
let searchTimeout = null;

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    initializeUpload();
    initializeSearch();
    initializeModals();
    initializeTooltips();
    loadDashboardStats();
}

/**
 * تهيئة نظام رفع الملفات
 */
function initializeUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('csvFile');
    const uploadForm = document.getElementById('uploadForm');
    
    if (!uploadArea || !fileInput || !uploadForm) return;
    
    // أحداث السحب والإفلات
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', () => fileInput.click());
    
    // تغيير الملف
    fileInput.addEventListener('change', handleFileSelect);
    
    // إرسال النموذج
    uploadForm.addEventListener('submit', handleUploadSubmit);
}

/**
 * معالجة السحب فوق المنطقة
 */
function handleDragOver(e) {
    e.preventDefault();
    e.currentTarget.classList.add('dragover');
}

/**
 * معالجة مغادرة منطقة السحب
 */
function handleDragLeave(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
}

/**
 * معالجة إفلات الملف
 */
function handleDrop(e) {
    e.preventDefault();
    e.currentTarget.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        document.getElementById('csvFile').files = files;
        handleFileSelect({ target: { files: files } });
    }
}

/**
 * معالجة اختيار الملف
 */
function handleFileSelect(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    // التحقق من نوع الملف
    const allowedTypes = ['text/csv', 'application/csv', 'text/plain'];
    const fileExtension = file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(file.type) && !['csv', 'txt'].includes(fileExtension)) {
        showAlert('نوع الملف غير مدعوم. يرجى اختيار ملف CSV أو TXT.', 'error');
        return;
    }
    
    // التحقق من حجم الملف (50 MB)
    if (file.size > 50 * 1024 * 1024) {
        showAlert('حجم الملف كبير جداً. الحد الأقصى المسموح 50 ميجابايت.', 'error');
        return;
    }
    
    // عرض معلومات الملف
    displayFileInfo(file);
}

/**
 * عرض معلومات الملف
 */
function displayFileInfo(file) {
    const fileInfo = document.getElementById('fileInfo');
    if (!fileInfo) return;
    
    fileInfo.innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-file-csv"></i>
            <div>
                <strong>الملف المحدد:</strong> ${file.name}<br>
                <strong>الحجم:</strong> ${formatFileSize(file.size)}<br>
                <strong>النوع:</strong> ${file.type || 'غير محدد'}
            </div>
        </div>
    `;
    fileInfo.classList.remove('hidden');
}

/**
 * معالجة إرسال نموذج الرفع
 */
async function handleUploadSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    try {
        // تعطيل الزر وإظهار التحميل
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner"></span> جاري الرفع...';
        
        // إرسال الطلب
        const response = await fetch('api/upload.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert(result.message, 'success');
            e.target.reset();
            document.getElementById('fileInfo').classList.add('hidden');
            
            // إعادة تحميل الإحصائيات
            loadDashboardStats();
            
            // الانتقال إلى صفحة البيانات
            setTimeout(() => {
                window.location.href = `view.php?table=${result.data.table_name}`;
            }, 2000);
        } else {
            showAlert(result.error, 'error');
        }
        
    } catch (error) {
        console.error('Upload error:', error);
        showAlert('حدث خطأ أثناء رفع الملف. يرجى المحاولة مرة أخرى.', 'error');
    } finally {
        // إعادة تفعيل الزر
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

/**
 * تهيئة نظام البحث
 */
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) return;
    
    searchInput.addEventListener('input', handleSearchInput);
}

/**
 * معالجة إدخال البحث
 */
function handleSearchInput(e) {
    const query = e.target.value.trim();
    
    // إلغاء البحث السابق
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    
    // بحث جديد بعد تأخير قصير
    searchTimeout = setTimeout(() => {
        performSearch(query);
    }, 300);
}

/**
 * تنفيذ البحث
 */
async function performSearch(query) {
    if (!currentTable) return;
    
    try {
        const response = await fetch(`api/search.php?table=${currentTable}&q=${encodeURIComponent(query)}`);
        const result = await response.json();
        
        if (result.success) {
            updateDataTable(result.data);
        } else {
            showAlert(result.error, 'error');
        }
        
    } catch (error) {
        console.error('Search error:', error);
        showAlert('حدث خطأ أثناء البحث.', 'error');
    }
}

/**
 * تحديث جدول البيانات
 */
function updateDataTable(data) {
    const tableBody = document.querySelector('#dataTable tbody');
    if (!tableBody) return;
    
    if (data.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="100%" class="text-center">لا توجد نتائج</td></tr>';
        return;
    }
    
    let html = '';
    data.forEach(row => {
        html += '<tr>';
        Object.values(row).forEach(value => {
            html += `<td>${escapeHtml(value)}</td>`;
        });
        html += '</tr>';
    });
    
    tableBody.innerHTML = html;
}

/**
 * تهيئة النوافذ المنبثقة
 */
function initializeModals() {
    // إغلاق النوافذ عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target.id);
        }
    });
    
    // إغلاق النوافذ بمفتاح Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                closeModal(openModal.id);
            }
        }
    });
}

/**
 * فتح نافذة منبثقة
 */
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

/**
 * إغلاق نافذة منبثقة
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }
}

/**
 * تهيئة التلميحات
 */
function initializeTooltips() {
    // يمكن إضافة مكتبة tooltips هنا إذا لزم الأمر
}

/**
 * تحميل إحصائيات لوحة التحكم
 */
async function loadDashboardStats() {
    try {
        const response = await fetch('api/stats.php');
        const result = await response.json();
        
        if (result.success) {
            updateStatsDisplay(result.data);
        }
        
    } catch (error) {
        console.error('Stats loading error:', error);
    }
}

/**
 * تحديث عرض الإحصائيات
 */
function updateStatsDisplay(stats) {
    const elements = {
        totalFiles: document.getElementById('totalFiles'),
        totalRecords: document.getElementById('totalRecords'),
        totalSize: document.getElementById('totalSize'),
        lastUpload: document.getElementById('lastUpload')
    };
    
    if (elements.totalFiles) elements.totalFiles.textContent = stats.total_files || 0;
    if (elements.totalRecords) elements.totalRecords.textContent = stats.total_records || 0;
    if (elements.totalSize) elements.totalSize.textContent = formatFileSize(stats.total_size || 0);
    if (elements.lastUpload) elements.lastUpload.textContent = stats.last_upload || 'لا يوجد';
}

/**
 * عرض رسالة تنبيه
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer') || createAlertContainer();
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        <i class="fas fa-${getAlertIcon(type)}"></i>
        <span>${message}</span>
        <button type="button" class="modal-close" onclick="this.parentElement.remove()">×</button>
    `;
    
    alertContainer.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}

/**
 * إنشاء حاوي التنبيهات
 */
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alertContainer';
    container.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    document.body.appendChild(container);
    return container;
}

/**
 * الحصول على أيقونة التنبيه
 */
function getAlertIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * تنسيق حجم الملف
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';
    
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * تشفير HTML
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * حذف ملف
 */
async function deleteFile(tableId, tableName) {
    if (!confirm('هل أنت متأكد من حذف هذا الملف؟ لا يمكن التراجع عن هذا الإجراء.')) {
        return;
    }
    
    try {
        const response = await fetch('api/delete.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                table_id: tableId,
                table_name: tableName,
                csrf_token: document.querySelector('meta[name="csrf-token"]').content
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('تم حذف الملف بنجاح', 'success');
            location.reload();
        } else {
            showAlert(result.error, 'error');
        }
        
    } catch (error) {
        console.error('Delete error:', error);
        showAlert('حدث خطأ أثناء حذف الملف', 'error');
    }
}
