<?php
/**
 * API تصدير البيانات
 * Data Export API
 */

require_once __DIR__ . '/../config/config.php';

// التأكد من أن الطلب GET
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendErrorResponse('طريقة الطلب غير صحيحة', 405);
}

// الحصول على المعاملات
$tableName = $_GET['table'] ?? '';
$format = $_GET['format'] ?? 'csv';
$searchQuery = $_GET['q'] ?? '';

// تنظيف المدخلات
$tableName = sanitizeInput($tableName);
$format = sanitizeInput($format);
$searchQuery = sanitizeInput($searchQuery);

// التحقق من صحة التنسيق
$allowedFormats = ['csv', 'excel', 'json'];
if (!in_array($format, $allowedFormats)) {
    sendErrorResponse('تنسيق التصدير غير مدعوم');
}

if (empty($tableName)) {
    sendErrorResponse('اسم الجدول مطلوب');
}

try {
    $db = getDB();
    
    // التحقق من وجود الجدول
    if (!$db->tableExists($tableName)) {
        sendErrorResponse('الجدول المطلوب غير موجود');
    }
    
    // الحصول على معلومات الملف
    $fileInfo = $db->fetchOne("
        SELECT original_name 
        FROM imported_files 
        WHERE table_name = ? AND status = 'completed'
    ", [$tableName]);
    
    if (!$fileInfo) {
        sendErrorResponse('معلومات الملف غير موجودة');
    }
    
    // الحصول على معلومات الأعمدة
    $columns = $db->getTableColumns($tableName);
    $columnNames = array_column($columns, 'Field');
    
    // إزالة الأعمدة النظام
    $dataColumns = array_filter($columnNames, function($col) {
        return !in_array($col, ['id', 'created_at', 'updated_at']);
    });
    
    // بناء استعلام البحث
    $searchConditions = [];
    $searchParams = [];
    
    if (!empty($searchQuery)) {
        foreach ($dataColumns as $column) {
            $searchConditions[] = "`$column` LIKE ?";
            $searchParams[] = "%$searchQuery%";
        }
    }
    
    $whereClause = '';
    if (!empty($searchConditions)) {
        $whereClause = 'WHERE ' . implode(' OR ', $searchConditions);
    }
    
    // الحصول على البيانات
    $columnsList = '`' . implode('`, `', $dataColumns) . '`';
    $sql = "SELECT $columnsList FROM `$tableName` $whereClause ORDER BY id";
    $data = $db->fetchAll($sql, $searchParams);
    
    // تسجيل النشاط
    logActivity('export', $tableName, "تصدير البيانات بصيغة $format");
    
    // تصدير البيانات حسب التنسيق
    switch ($format) {
        case 'csv':
            exportCSV($data, $dataColumns, $fileInfo['original_name']);
            break;
        case 'excel':
            exportExcel($data, $dataColumns, $fileInfo['original_name']);
            break;
        case 'json':
            exportJSON($data, $fileInfo['original_name']);
            break;
    }
    
} catch (Exception $e) {
    error_log("Export error: " . $e->getMessage());
    sendErrorResponse('حدث خطأ أثناء التصدير: ' . $e->getMessage());
}

/**
 * تصدير CSV
 */
function exportCSV($data, $columns, $filename) {
    $filename = pathinfo($filename, PATHINFO_FILENAME) . '.csv';
    
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    // إضافة BOM للدعم الصحيح للعربية في Excel
    echo "\xEF\xBB\xBF";
    
    $output = fopen('php://output', 'w');
    
    // كتابة رأس الأعمدة
    fputcsv($output, $columns);
    
    // كتابة البيانات
    foreach ($data as $row) {
        $rowData = [];
        foreach ($columns as $column) {
            $rowData[] = $row[$column] ?? '';
        }
        fputcsv($output, $rowData);
    }
    
    fclose($output);
    exit;
}

/**
 * تصدير Excel (CSV محسن)
 */
function exportExcel($data, $columns, $filename) {
    $filename = pathinfo($filename, PATHINFO_FILENAME) . '.xlsx.csv';
    
    header('Content-Type: application/vnd.ms-excel; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    // إضافة BOM للدعم الصحيح للعربية
    echo "\xEF\xBB\xBF";
    
    $output = fopen('php://output', 'w');
    
    // كتابة رأس الأعمدة
    fputcsv($output, $columns, "\t");
    
    // كتابة البيانات
    foreach ($data as $row) {
        $rowData = [];
        foreach ($columns as $column) {
            $value = $row[$column] ?? '';
            // تنسيق خاص للأرقام في Excel
            if (is_numeric($value) && strlen($value) > 10) {
                $value = "=\"$value\""; // منع تحويل الأرقام الطويلة إلى تنسيق علمي
            }
            $rowData[] = $value;
        }
        fputcsv($output, $rowData, "\t");
    }
    
    fclose($output);
    exit;
}

/**
 * تصدير JSON
 */
function exportJSON($data, $filename) {
    $filename = pathinfo($filename, PATHINFO_FILENAME) . '.json';
    
    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    $jsonData = [
        'exported_at' => date('Y-m-d H:i:s'),
        'total_records' => count($data),
        'data' => $data
    ];
    
    echo json_encode($jsonData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}
?>
