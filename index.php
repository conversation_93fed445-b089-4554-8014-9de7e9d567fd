<?php
/**
 * الصفحة الرئيسية - مدير ملفات CSV
 * Main Page - CSV Manager
 */

require_once 'config/config.php';

// الحصول على إحصائيات سريعة
try {
    $db = getDB();
    
    // إجمالي الملفات
    $totalFiles = $db->fetchOne("SELECT COUNT(*) as count FROM imported_files WHERE status = 'completed'")['count'] ?? 0;
    
    // إجمالي السجلات
    $totalRecords = $db->fetchOne("SELECT SUM(total_rows) as total FROM imported_files WHERE status = 'completed'")['total'] ?? 0;
    
    // إجمالي حجم الملفات
    $totalSize = $db->fetchOne("SELECT SUM(file_size) as total FROM imported_files WHERE status = 'completed'")['total'] ?? 0;
    
    // آخر ملف تم رفعه
    $lastUpload = $db->fetchOne("SELECT original_name, upload_date FROM imported_files WHERE status = 'completed' ORDER BY upload_date DESC LIMIT 1");
    
} catch (Exception $e) {
    $totalFiles = $totalRecords = $totalSize = 0;
    $lastUpload = null;
}

$pageTitle = 'الصفحة الرئيسية';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <title><?php echo $pageTitle . ' - ' . SITE_TITLE; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-file-csv"></i>
                    <?php echo SITE_TITLE; ?>
                </div>
                <nav class="nav">
                    <a href="index.php" class="nav-link active">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                    <a href="files.php" class="nav-link">
                        <i class="fas fa-folder"></i>
                        الملفات
                    </a>
                    <a href="search.php" class="nav-link">
                        <i class="fas fa-search"></i>
                        البحث
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="container">
            <!-- الإحصائيات السريعة -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number" id="totalFiles"><?php echo number_format($totalFiles); ?></div>
                    <div class="stat-label">إجمالي الملفات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalRecords"><?php echo number_format($totalRecords); ?></div>
                    <div class="stat-label">إجمالي السجلات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalSize"><?php echo formatFileSize($totalSize); ?></div>
                    <div class="stat-label">إجمالي الحجم</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="lastUpload">
                        <?php echo $lastUpload ? date('Y-m-d', strtotime($lastUpload['upload_date'])) : 'لا يوجد'; ?>
                    </div>
                    <div class="stat-label">آخر رفع</div>
                </div>
            </div>

            <!-- رفع ملف جديد -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-upload"></i>
                        رفع ملف CSV جديد
                    </h2>
                    <p class="card-description">
                        اختر ملف CSV لاستيراده إلى قاعدة البيانات. يدعم النظام الملفات بصيغة CSV و TXT مع ترميز UTF-8.
                    </p>
                </div>

                <form id="uploadForm" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <!-- منطقة رفع الملف -->
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">اسحب وأفلت ملف CSV هنا أو انقر للاختيار</div>
                        <div class="upload-hint">الحد الأقصى: 50 ميجابايت | الأنواع المدعومة: CSV, TXT</div>
                        <input type="file" id="csvFile" name="csv_file" accept=".csv,.txt" style="display: none;">
                    </div>

                    <!-- معلومات الملف -->
                    <div id="fileInfo" class="hidden"></div>

                    <!-- خيارات متقدمة -->
                    <div class="grid grid-2 mt-4">
                        <div class="form-group">
                            <label for="delimiter" class="form-label">فاصل الأعمدة</label>
                            <select id="delimiter" name="delimiter" class="form-select">
                                <option value=",">فاصلة (,)</option>
                                <option value=";">فاصلة منقوطة (;)</option>
                                <option value="\t">تبويب (Tab)</option>
                                <option value="|">خط عمودي (|)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="encoding" class="form-label">ترميز الملف</label>
                            <select id="encoding" name="encoding" class="form-select">
                                <option value="UTF-8">UTF-8</option>
                                <option value="Windows-1256">Windows-1256</option>
                                <option value="ISO-8859-6">ISO-8859-6</option>
                            </select>
                        </div>
                    </div>

                    <!-- زر الرفع -->
                    <div class="form-group mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i>
                            رفع الملف
                        </button>
                    </div>
                </form>
            </div>

            <!-- الملفات الأخيرة -->
            <?php if ($totalFiles > 0): ?>
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-history"></i>
                        الملفات الأخيرة
                    </h2>
                </div>

                <?php
                try {
                    $recentFiles = $db->fetchAll("
                        SELECT id, original_name, table_name, total_rows, file_size, upload_date 
                        FROM imported_files 
                        WHERE status = 'completed' 
                        ORDER BY upload_date DESC 
                        LIMIT 5
                    ");
                ?>

                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم الملف</th>
                                <th>عدد السجلات</th>
                                <th>الحجم</th>
                                <th>تاريخ الرفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentFiles as $file): ?>
                            <tr>
                                <td>
                                    <i class="fas fa-file-csv text-primary"></i>
                                    <?php echo htmlspecialchars($file['original_name']); ?>
                                </td>
                                <td><?php echo number_format($file['total_rows']); ?></td>
                                <td><?php echo formatFileSize($file['file_size']); ?></td>
                                <td><?php echo date('Y-m-d H:i', strtotime($file['upload_date'])); ?></td>
                                <td>
                                    <a href="view.php?table=<?php echo urlencode($file['table_name']); ?>" 
                                       class="btn btn-outline btn-sm">
                                        <i class="fas fa-eye"></i>
                                        عرض
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <div class="text-center mt-3">
                    <a href="files.php" class="btn btn-outline">
                        <i class="fas fa-list"></i>
                        عرض جميع الملفات
                    </a>
                </div>

                <?php
                } catch (Exception $e) {
                    echo '<div class="alert alert-error">خطأ في تحميل الملفات الأخيرة</div>';
                }
                ?>
            </div>
            <?php endif; ?>

            <!-- معلومات النظام -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i class="fas fa-info-circle"></i>
                        حول النظام
                    </h2>
                </div>
                <div class="grid grid-2">
                    <div>
                        <h3 class="font-semibold mb-2">الميزات الرئيسية:</h3>
                        <ul style="list-style: none; padding: 0;">
                            <li class="mb-1"><i class="fas fa-check text-success"></i> استيراد ملفات CSV بدون قيود</li>
                            <li class="mb-1"><i class="fas fa-check text-success"></i> إنشاء جداول ديناميكية تلقائياً</li>
                            <li class="mb-1"><i class="fas fa-check text-success"></i> بحث فوري في البيانات</li>
                            <li class="mb-1"><i class="fas fa-check text-success"></i> واجهة مودرن تدعم العربية</li>
                            <li class="mb-1"><i class="fas fa-check text-success"></i> تصدير البيانات بصيغ متعددة</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-2">المواصفات التقنية:</h3>
                        <ul style="list-style: none; padding: 0;">
                            <li class="mb-1"><i class="fas fa-server text-primary"></i> PHP + MySQL</li>
                            <li class="mb-1"><i class="fas fa-database text-primary"></i> قاعدة بيانات UTF-8</li>
                            <li class="mb-1"><i class="fas fa-mobile-alt text-primary"></i> تصميم متجاوب</li>
                            <li class="mb-1"><i class="fas fa-shield-alt text-primary"></i> حماية CSRF</li>
                            <li class="mb-1"><i class="fas fa-language text-primary"></i> دعم كامل للعربية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- الفوتر -->
    <footer class="footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_TITLE; ?>. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
</body>
</html>
