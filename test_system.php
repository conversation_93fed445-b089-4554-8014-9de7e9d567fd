<?php
/**
 * اختبار شامل للنظام
 * System Comprehensive Test
 */

require_once 'config/config.php';

// تعطيل عرض الأخطاء للاختبار
error_reporting(0);
ini_set('display_errors', 0);

$tests = [];
$passed = 0;
$failed = 0;

/**
 * دالة إضافة اختبار
 */
function addTest($name, $result, $message = '') {
    global $tests, $passed, $failed;
    
    $tests[] = [
        'name' => $name,
        'result' => $result,
        'message' => $message
    ];
    
    if ($result) {
        $passed++;
    } else {
        $failed++;
    }
}

// اختبار الاتصال بقاعدة البيانات
try {
    $db = getDB();
    $connection = $db->getConnection();
    addTest('اتصال قاعدة البيانات', true, 'تم الاتصال بنجاح');
} catch (Exception $e) {
    addTest('اتصال قاعدة البيانات', false, $e->getMessage());
}

// اختبار وجود الجداول المطلوبة
$requiredTables = ['imported_files', 'system_settings', 'activity_log', 'user_sessions'];

// الحصول على قائمة الجداول الموجودة
try {
    $existingTables = $db->getAllTables();
    addTest('قائمة الجداول', !empty($existingTables), 'تم العثور على ' . count($existingTables) . ' جدول: ' . implode(', ', $existingTables));
} catch (Exception $e) {
    addTest('قائمة الجداول', false, $e->getMessage());
    $existingTables = [];
}

// فحص كل جدول مطلوب
foreach ($requiredTables as $table) {
    try {
        $exists = $db->tableExists($table);

        if ($exists) {
            // إذا كان الجدول موجود، اختبر الوصول إليه
            try {
                $count = $db->fetchOne("SELECT COUNT(*) as count FROM `$table`");
                $recordCount = $count ? $count['count'] : 0;
                addTest("جدول $table", true, "موجود ويحتوي على $recordCount سجل");
            } catch (Exception $e) {
                addTest("جدول $table", false, "موجود لكن لا يمكن الوصول إليه: " . $e->getMessage());
            }
        } else {
            addTest("جدول $table", false, 'غير موجود');
        }
    } catch (Exception $e) {
        addTest("جدول $table", false, $e->getMessage());
    }
}

// اختبار إعدادات النظام
try {
    $settings = getSystemSettings();
    $hasSettings = !empty($settings);
    addTest('إعدادات النظام', $hasSettings, $hasSettings ? count($settings) . ' إعداد محمل' : 'لا توجد إعدادات');
} catch (Exception $e) {
    addTest('إعدادات النظام', false, $e->getMessage());
}

// اختبار المجلدات المطلوبة
$requiredDirs = ['uploads', 'logs', 'config', 'api', 'assets'];
foreach ($requiredDirs as $dir) {
    $exists = is_dir($dir);
    $writable = $exists && is_writable($dir);
    addTest("مجلد $dir", $exists, $exists ? ($writable ? 'موجود وقابل للكتابة' : 'موجود لكن غير قابل للكتابة') : 'غير موجود');
}

// اختبار الملفات المطلوبة
$requiredFiles = [
    'index.php' => 'الصفحة الرئيسية',
    'files.php' => 'صفحة الملفات',
    'search.php' => 'صفحة البحث',
    'view.php' => 'صفحة العرض',
    'config/config.php' => 'ملف الإعدادات',
    'config/database.php' => 'ملف قاعدة البيانات',
    'includes/CSVImporter.php' => 'مستورد CSV',
    'api/upload.php' => 'API الرفع',
    'api/search.php' => 'API البحث',
    'api/export.php' => 'API التصدير',
    'api/delete.php' => 'API الحذف',
    'assets/css/style.css' => 'ملف الأنماط',
    'assets/js/main.js' => 'ملف JavaScript'
];

foreach ($requiredFiles as $file => $description) {
    $exists = file_exists($file);
    addTest($description, $exists, $exists ? 'موجود' : 'غير موجود');
}

// اختبار إعدادات PHP
$phpSettings = [
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
    'max_execution_time' => ini_get('max_execution_time'),
    'memory_limit' => ini_get('memory_limit')
];

foreach ($phpSettings as $setting => $value) {
    addTest("إعداد PHP: $setting", !empty($value), "القيمة: $value");
}

// اختبار امتدادات PHP المطلوبة
$requiredExtensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'fileinfo'];
foreach ($requiredExtensions as $ext) {
    $loaded = extension_loaded($ext);
    addTest("امتداد PHP: $ext", $loaded, $loaded ? 'محمل' : 'غير محمل');
}

// اختبار إنشاء جدول ديناميكي
try {
    $testTableName = 'test_table_' . time();
    $testColumns = ['اسم', 'عمر', 'مدينة'];
    
    $db->createDynamicTable($testTableName, $testColumns);
    $tableCreated = $db->tableExists($testTableName);
    
    if ($tableCreated) {
        // حذف الجدول التجريبي
        $db->execute("DROP TABLE `$testTableName`");
        addTest('إنشاء جدول ديناميكي', true, 'تم إنشاء وحذف الجدول بنجاح');
    } else {
        addTest('إنشاء جدول ديناميكي', false, 'فشل في إنشاء الجدول');
    }
} catch (Exception $e) {
    addTest('إنشاء جدول ديناميكي', false, $e->getMessage());
}

// اختبار CSRF Token
try {
    $token = generateCSRFToken();
    $isValid = verifyCSRFToken($token);
    addTest('CSRF Token', $isValid, $isValid ? 'يعمل بشكل صحيح' : 'لا يعمل');
} catch (Exception $e) {
    addTest('CSRF Token', false, $e->getMessage());
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام - مدير ملفات CSV</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            padding: 20px;
            background: #f1f5f9;
        }
        .stat {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .stat-number.success { color: #059669; }
        .stat-number.danger { color: #dc2626; }
        .stat-label {
            font-size: 0.9rem;
            color: #64748b;
        }
        .tests {
            padding: 20px;
        }
        .test-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #e2e8f0;
            gap: 15px;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-icon {
            font-size: 1.2rem;
            width: 20px;
            text-align: center;
        }
        .test-icon.success { color: #059669; }
        .test-icon.danger { color: #dc2626; }
        .test-name {
            flex: 1;
            font-weight: 500;
        }
        .test-message {
            font-size: 0.9rem;
            color: #64748b;
        }
        .actions {
            padding: 20px;
            text-align: center;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 0 10px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .btn-success {
            background: #059669;
        }
        .btn-success:hover {
            background: #047857;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-check-circle"></i> اختبار شامل للنظام</h1>
            <p>فحص جميع مكونات مدير ملفات CSV</p>
        </div>

        <div class="stats">
            <div class="stat">
                <div class="stat-number success"><?php echo $passed; ?></div>
                <div class="stat-label">اختبارات ناجحة</div>
            </div>
            <div class="stat">
                <div class="stat-number danger"><?php echo $failed; ?></div>
                <div class="stat-label">اختبارات فاشلة</div>
            </div>
            <div class="stat">
                <div class="stat-number"><?php echo count($tests); ?></div>
                <div class="stat-label">إجمالي الاختبارات</div>
            </div>
            <div class="stat">
                <div class="stat-number <?php echo $failed === 0 ? 'success' : 'danger'; ?>">
                    <?php echo $failed === 0 ? '100%' : round(($passed / count($tests)) * 100) . '%'; ?>
                </div>
                <div class="stat-label">معدل النجاح</div>
            </div>
        </div>

        <div class="tests">
            <h3><i class="fas fa-list"></i> تفاصيل الاختبارات</h3>
            <?php foreach ($tests as $test): ?>
            <div class="test-item">
                <div class="test-icon <?php echo $test['result'] ? 'success' : 'danger'; ?>">
                    <i class="fas fa-<?php echo $test['result'] ? 'check' : 'times'; ?>"></i>
                </div>
                <div class="test-name"><?php echo htmlspecialchars($test['name']); ?></div>
                <div class="test-message"><?php echo htmlspecialchars($test['message']); ?></div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="actions">
            <?php if ($failed === 0): ?>
            <div style="margin-bottom: 15px;">
                <i class="fas fa-check-circle" style="color: #059669; font-size: 2rem;"></i>
                <h3 style="color: #059669; margin: 10px 0;">النظام جاهز للاستخدام!</h3>
                <p>جميع الاختبارات نجحت. يمكنك الآن استخدام النظام بأمان.</p>
            </div>
            <a href="index.php" class="btn btn-success">
                <i class="fas fa-home"></i> الانتقال للصفحة الرئيسية
            </a>
            <?php else: ?>
            <div style="margin-bottom: 15px;">
                <i class="fas fa-exclamation-triangle" style="color: #dc2626; font-size: 2rem;"></i>
                <h3 style="color: #dc2626; margin: 10px 0;">يوجد مشاكل تحتاج إصلاح</h3>
                <p>يرجى مراجعة الاختبارات الفاشلة وإصلاحها قبل استخدام النظام.</p>
            </div>
            <a href="INSTALL.md" class="btn">
                <i class="fas fa-book"></i> دليل التثبيت
            </a>
            <?php endif; ?>
            
            <a href="?refresh=1" class="btn">
                <i class="fas fa-sync-alt"></i> إعادة الاختبار
            </a>
        </div>
    </div>

    <script>
        // تحديث تلقائي كل 30 ثانية إذا كانت هناك أخطاء
        <?php if ($failed > 0): ?>
        setTimeout(function() {
            if (confirm('هل تريد إعادة تشغيل الاختبار؟')) {
                location.reload();
            }
        }, 30000);
        <?php endif; ?>
    </script>
</body>
</html>
