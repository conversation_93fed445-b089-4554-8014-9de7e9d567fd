<?php
/**
 * فئة استيراد ملفات CSV
 * CSV Import Class
 */

require_once __DIR__ . '/../config/config.php';

class CSVImporter {
    private $db;
    private $allowedExtensions;
    private $maxFileSize;
    
    public function __construct() {
        $this->db = getDB();
        $this->allowedExtensions = ALLOWED_EXTENSIONS;
        $this->maxFileSize = MAX_FILE_SIZE;
    }
    
    /**
     * استيراد ملف CSV
     */
    public function importFile($file, $options = []) {
        try {
            // التحقق من صحة الملف
            $validation = $this->validateFile($file);
            if (!$validation['valid']) {
                throw new Exception($validation['error']);
            }
            
            // إنشاء اسم جدول فريد
            $tableName = generateTableName($file['name']);
            
            // قراءة محتوى الملف
            $csvData = $this->readCSVFile($file['tmp_name'], $options);
            
            if (empty($csvData)) {
                throw new Exception('الملف فارغ أو لا يحتوي على بيانات صالحة');
            }
            
            // الحصول على أسماء الأعمدة
            $headers = array_shift($csvData);
            $headers = $this->sanitizeHeaders($headers);
            
            // بدء معاملة قاعدة البيانات
            $this->db->beginTransaction();
            
            try {
                // إنشاء الجدول
                $this->createTable($tableName, $headers);
                
                // إدراج البيانات
                $insertedRows = $this->insertData($tableName, $headers, $csvData);
                
                // حفظ معلومات الملف
                $fileId = $this->saveFileInfo($file, $tableName, $headers, $insertedRows);
                
                // تأكيد المعاملة
                $this->db->commit();
                
                // تسجيل النشاط
                logActivity('upload', $tableName, "تم استيراد ملف {$file['name']} بنجاح - $insertedRows سجل");
                
                return [
                    'success' => true,
                    'file_id' => $fileId,
                    'table_name' => $tableName,
                    'total_rows' => $insertedRows,
                    'columns' => $headers,
                    'message' => "تم استيراد الملف بنجاح. تم إدراج $insertedRows سجل."
                ];
                
            } catch (Exception $e) {
                $this->db->rollback();
                throw $e;
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * التحقق من صحة الملف
     */
    private function validateFile($file) {
        // التحقق من وجود أخطاء في الرفع
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return [
                'valid' => false,
                'error' => $this->getUploadErrorMessage($file['error'])
            ];
        }
        
        // التحقق من حجم الملف
        if ($file['size'] > $this->maxFileSize) {
            return [
                'valid' => false,
                'error' => 'حجم الملف كبير جداً. الحد الأقصى المسموح: ' . formatFileSize($this->maxFileSize)
            ];
        }
        
        // التحقق من امتداد الملف
        if (!isValidFileExtension($file['name'])) {
            return [
                'valid' => false,
                'error' => 'نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', $this->allowedExtensions)
            ];
        }
        
        // التحقق من أن الملف موجود
        if (!is_uploaded_file($file['tmp_name'])) {
            return [
                'valid' => false,
                'error' => 'خطأ في رفع الملف'
            ];
        }
        
        return ['valid' => true];
    }
    
    /**
     * قراءة ملف CSV
     */
    private function readCSVFile($filePath, $options = []) {
        $delimiter = $options['delimiter'] ?? ',';
        $enclosure = $options['enclosure'] ?? '"';
        $escape = $options['escape'] ?? '\\';
        $encoding = $options['encoding'] ?? 'UTF-8';
        
        $data = [];
        
        // قراءة الملف
        $content = file_get_contents($filePath);
        
        // تحويل الترميز إذا لزم الأمر
        if ($encoding !== 'UTF-8') {
            $content = mb_convert_encoding($content, 'UTF-8', $encoding);
        }
        
        // تحويل النهايات المختلفة للأسطر
        $content = str_replace(["\r\n", "\r"], "\n", $content);
        
        // تقسيم إلى أسطر
        $lines = explode("\n", $content);
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            $row = str_getcsv($line, $delimiter, $enclosure, $escape);
            if (!empty($row) && !empty(array_filter($row))) {
                $data[] = $row;
            }
        }
        
        return $data;
    }
    
    /**
     * تنظيف أسماء الأعمدة
     */
    private function sanitizeHeaders($headers) {
        $sanitized = [];
        $used = [];
        
        foreach ($headers as $header) {
            $clean = $this->sanitizeColumnName($header);
            
            // التأكد من عدم التكرار
            $original = $clean;
            $counter = 1;
            while (in_array($clean, $used)) {
                $clean = $original . '_' . $counter;
                $counter++;
            }
            
            $used[] = $clean;
            $sanitized[] = $clean;
        }
        
        return $sanitized;
    }
    
    /**
     * تنظيف اسم العمود
     */
    private function sanitizeColumnName($name) {
        $name = trim($name);
        $name = preg_replace('/[^\p{L}\p{N}_]/u', '_', $name);
        $name = preg_replace('/_+/', '_', $name);
        $name = trim($name, '_');
        
        if (preg_match('/^\d/', $name)) {
            $name = 'col_' . $name;
        }
        
        if (empty($name)) {
            $name = 'column_' . uniqid();
        }
        
        return $name;
    }
    
    /**
     * إنشاء جدول ديناميكي
     */
    private function createTable($tableName, $headers) {
        $sql = "CREATE TABLE IF NOT EXISTS `$tableName` (";
        $sql .= "id INT AUTO_INCREMENT PRIMARY KEY,";
        
        foreach ($headers as $header) {
            $sql .= "`$header` TEXT,";
        }
        
        $sql .= "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,";
        $sql .= "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,";
        $sql .= "INDEX idx_created_at (created_at)";
        $sql .= ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->execute($sql);
    }
    
    /**
     * إدراج البيانات
     */
    private function insertData($tableName, $headers, $data) {
        if (empty($data)) return 0;
        
        $placeholders = str_repeat('?,', count($headers));
        $placeholders = rtrim($placeholders, ',');
        
        $columns = '`' . implode('`, `', $headers) . '`';
        $sql = "INSERT INTO `$tableName` ($columns) VALUES ($placeholders)";
        
        $insertedRows = 0;
        
        foreach ($data as $row) {
            // التأكد من أن عدد القيم يطابق عدد الأعمدة
            $row = array_pad($row, count($headers), '');
            $row = array_slice($row, 0, count($headers));
            
            try {
                $this->db->execute($sql, $row);
                $insertedRows++;
            } catch (Exception $e) {
                // تسجيل الخطأ ومتابعة العملية
                error_log("Error inserting row: " . $e->getMessage());
            }
        }
        
        return $insertedRows;
    }
    
    /**
     * حفظ معلومات الملف
     */
    private function saveFileInfo($file, $tableName, $headers, $totalRows) {
        $sql = "INSERT INTO imported_files 
                (file_name, original_name, table_name, file_size, total_rows, columns_info, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            basename($file['tmp_name']),
            $file['name'],
            $tableName,
            $file['size'],
            $totalRows,
            json_encode($headers, JSON_UNESCAPED_UNICODE),
            'completed'
        ];
        
        return $this->db->insert($sql, $params);
    }
    
    /**
     * الحصول على رسالة خطأ الرفع
     */
    private function getUploadErrorMessage($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'حجم الملف يتجاوز الحد المسموح في إعدادات الخادم';
            case UPLOAD_ERR_FORM_SIZE:
                return 'حجم الملف يتجاوز الحد المسموح في النموذج';
            case UPLOAD_ERR_PARTIAL:
                return 'تم رفع جزء من الملف فقط';
            case UPLOAD_ERR_NO_FILE:
                return 'لم يتم رفع أي ملف';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'مجلد الملفات المؤقتة غير موجود';
            case UPLOAD_ERR_CANT_WRITE:
                return 'فشل في كتابة الملف على القرص';
            case UPLOAD_ERR_EXTENSION:
                return 'امتداد PHP أوقف رفع الملف';
            default:
                return 'خطأ غير معروف في رفع الملف';
        }
    }
}
?>
