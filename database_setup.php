<?php
/**
 * إعداد قاعدة البيانات التلقائي
 * Automatic Database Setup
 * 
 * هذا الملف يقوم بإعداد قاعدة البيانات والمشروع بالكامل تلقائياً
 * This file automatically sets up the database and project completely
 */

// تعطيل عرض الأخطاء مؤقتاً
error_reporting(E_ALL);
ini_set('display_errors', 1);

// إعدادات قاعدة البيانات - يمكن تعديلها حسب الحاجة
$config = [
    'db_host' => 'localhost',
    'db_name' => 'csv_manager',
    'db_user' => 'root',
    'db_pass' => '', // ضع كلمة المرور هنا إذا لزم الأمر
    'db_charset' => 'utf8mb4'
];

// التحقق من المتغيرات البيئية أو الإعدادات المخصصة
if (isset($_GET['db_host'])) $config['db_host'] = $_GET['db_host'];
if (isset($_GET['db_name'])) $config['db_name'] = $_GET['db_name'];
if (isset($_GET['db_user'])) $config['db_user'] = $_GET['db_user'];
if (isset($_GET['db_pass'])) $config['db_pass'] = $_GET['db_pass'];

$setup_steps = [];
$errors = [];
$success = true;

/**
 * دالة إضافة خطوة
 */
function addStep($step, $status, $message = '') {
    global $setup_steps;
    $setup_steps[] = [
        'step' => $step,
        'status' => $status,
        'message' => $message,
        'time' => date('H:i:s')
    ];
}

/**
 * دالة إضافة خطأ
 */
function addError($error) {
    global $errors, $success;
    $errors[] = $error;
    $success = false;
}

/**
 * دالة الحصول على SQL لإنشاء الجداول
 */
function getTableSQL($tableName) {
    $sqls = [
        'imported_files' => "
            CREATE TABLE IF NOT EXISTS imported_files (
                id INT AUTO_INCREMENT PRIMARY KEY,
                file_name VARCHAR(255) NOT NULL,
                original_name VARCHAR(255) NOT NULL,
                table_name VARCHAR(255) NOT NULL UNIQUE,
                file_size BIGINT NOT NULL,
                total_rows INT DEFAULT 0,
                columns_info JSON,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                status ENUM('processing', 'completed', 'error') DEFAULT 'processing',
                error_message TEXT NULL,
                INDEX idx_table_name (table_name),
                INDEX idx_upload_date (upload_date),
                INDEX idx_status (status)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",

        'system_settings' => "
            CREATE TABLE IF NOT EXISTS system_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(100) NOT NULL UNIQUE,
                setting_value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",

        'activity_log' => "
            CREATE TABLE IF NOT EXISTS activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                action_type ENUM('upload', 'delete', 'update', 'search', 'export') NOT NULL,
                table_name VARCHAR(255),
                description TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_action_type (action_type),
                INDEX idx_table_name (table_name),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",

        'user_sessions' => "
            CREATE TABLE IF NOT EXISTS user_sessions (
                id VARCHAR(128) PRIMARY KEY,
                session_data TEXT,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                ip_address VARCHAR(45),
                user_agent TEXT,
                INDEX idx_last_activity (last_activity)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];

    return isset($sqls[$tableName]) ? $sqls[$tableName] : null;
}

/**
 * دالة إدراج الإعدادات الافتراضية
 */
function insertDefaultSettings($pdo) {
    $settings = [
        ['max_file_size', '250', 'الحد الأقصى لحجم الملف بالميجابايت'],
        ['allowed_extensions', 'csv,txt', 'امتدادات الملفات المسموحة'],
        ['records_per_page', '50', 'عدد السجلات في الصفحة الواحدة'],
        ['site_title', 'مدير ملفات CSV', 'عنوان الموقع'],
        ['site_description', 'نظام إدارة واستيراد ملفات CSV مع الاستعلام الفوري', 'وصف الموقع'],
        ['default_language', 'ar', 'اللغة الافتراضية للموقع'],
        ['date_format', 'Y-m-d H:i:s', 'تنسيق التاريخ'],
        ['timezone', 'Asia/Riyadh', 'المنطقة الزمنية']
    ];

    $inserted = 0;
    foreach ($settings as $setting) {
        try {
            $sql = "INSERT INTO system_settings (setting_key, setting_value, description)
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE
                    setting_value = VALUES(setting_value),
                    updated_at = CURRENT_TIMESTAMP";

            $stmt = $pdo->prepare($sql);
            $stmt->execute($setting);
            $inserted++;
        } catch (PDOException $e) {
            error_log("Error inserting setting {$setting[0]}: " . $e->getMessage());
        }
    }

    return $inserted;
}

// بدء عملية الإعداد
addStep('بدء عملية الإعداد', 'info', 'تحضير إعداد قاعدة البيانات والمشروع');

try {
    // الخطوة 1: التحقق من متطلبات PHP
    addStep('فحص متطلبات PHP', 'progress', 'التحقق من الامتدادات المطلوبة...');
    
    $required_extensions = ['pdo', 'pdo_mysql', 'mbstring', 'json', 'fileinfo'];
    $missing_extensions = [];
    
    foreach ($required_extensions as $ext) {
        if (!extension_loaded($ext)) {
            $missing_extensions[] = $ext;
        }
    }
    
    if (!empty($missing_extensions)) {
        addError('امتدادات PHP مفقودة: ' . implode(', ', $missing_extensions));
        addStep('فحص متطلبات PHP', 'error', 'امتدادات مفقودة: ' . implode(', ', $missing_extensions));
    } else {
        addStep('فحص متطلبات PHP', 'success', 'جميع الامتدادات المطلوبة متوفرة');
    }
    
    // الخطوة 2: إنشاء المجلدات المطلوبة
    addStep('إنشاء المجلدات', 'progress', 'إنشاء مجلدات النظام...');

    $required_dirs = ['uploads', 'logs'];
    $created_dirs = [];
    $existing_dirs = [];
    $failed_dirs = [];

    foreach ($required_dirs as $dir) {
        if (!is_dir($dir)) {
            if (mkdir($dir, 0755, true)) {
                $created_dirs[] = $dir;
            } else {
                $failed_dirs[] = $dir;
                addError("فشل في إنشاء مجلد: $dir");
            }
        } else {
            $existing_dirs[] = $dir;
        }
    }

    if (empty($failed_dirs)) {
        $message = '';
        if (!empty($created_dirs)) {
            $message .= 'تم إنشاء: ' . implode(', ', $created_dirs);
        }
        if (!empty($existing_dirs)) {
            if (!empty($message)) $message .= ' | ';
            $message .= 'موجود مسبقاً: ' . implode(', ', $existing_dirs);
        }
        if (empty($message)) {
            $message = 'جميع المجلدات جاهزة';
        }
        addStep('إنشاء المجلدات', 'success', $message);
    } else {
        addStep('إنشاء المجلدات', 'error', 'فشل في إنشاء: ' . implode(', ', $failed_dirs));
    }
    
    // الخطوة 3: الاتصال بخادم قاعدة البيانات
    addStep('الاتصال بخادم قاعدة البيانات', 'progress', 'محاولة الاتصال...');
    
    try {
        $dsn = "mysql:host={$config['db_host']};charset={$config['db_charset']}";
        $pdo = new PDO($dsn, $config['db_user'], $config['db_pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$config['db_charset']} COLLATE {$config['db_charset']}_unicode_ci"
        ]);
        
        addStep('الاتصال بخادم قاعدة البيانات', 'success', 'تم الاتصال بنجاح');
        
    } catch (PDOException $e) {
        addError('فشل الاتصال بخادم قاعدة البيانات: ' . $e->getMessage());
        addStep('الاتصال بخادم قاعدة البيانات', 'error', $e->getMessage());
        throw $e;
    }
    
    // الخطوة 4: إنشاء قاعدة البيانات
    addStep('إنشاء قاعدة البيانات', 'progress', "إنشاء قاعدة البيانات: {$config['db_name']}");
    
    try {
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['db_name']}` CHARACTER SET {$config['db_charset']} COLLATE {$config['db_charset']}_unicode_ci");
        $pdo->exec("USE `{$config['db_name']}`");
        
        addStep('إنشاء قاعدة البيانات', 'success', "تم إنشاء قاعدة البيانات: {$config['db_name']}");
        
    } catch (PDOException $e) {
        addError('فشل في إنشاء قاعدة البيانات: ' . $e->getMessage());
        addStep('إنشاء قاعدة البيانات', 'error', $e->getMessage());
        throw $e;
    }
    
    // الخطوة 5: قراءة وتنفيذ ملف setup.sql
    addStep('إنشاء الجداول', 'progress', 'قراءة ملف setup.sql وإنشاء الجداول...');

    $sql_file = 'database/setup.sql';
    if (!file_exists($sql_file)) {
        addError("ملف setup.sql غير موجود في: $sql_file");
        addStep('إنشاء الجداول', 'error', 'ملف setup.sql غير موجود');
    } else {
        try {
            $sql_content = file_get_contents($sql_file);

            if (empty($sql_content)) {
                addError('ملف setup.sql فارغ');
                addStep('إنشاء الجداول', 'error', 'ملف setup.sql فارغ');
            } else {
                // تنظيف المحتوى وإزالة التعليقات
                $sql_content = preg_replace('/--.*$/m', '', $sql_content);
                $sql_content = preg_replace('/\/\*.*?\*\//s', '', $sql_content);

                // تقسيم الاستعلامات بطريقة أفضل
                $statements = [];
                $current_statement = '';
                $lines = explode("\n", $sql_content);

                foreach ($lines as $line) {
                    $line = trim($line);
                    if (empty($line)) continue;

                    $current_statement .= $line . ' ';

                    // إذا انتهى السطر بفاصلة منقوطة، فهو نهاية استعلام
                    if (substr($line, -1) === ';') {
                        $statement = trim($current_statement);
                        if (!empty($statement) && strlen($statement) > 10) {
                            $statements[] = $statement;
                        }
                        $current_statement = '';
                    }
                }

                // إضافة آخر استعلام إذا لم ينته بفاصلة منقوطة
                if (!empty(trim($current_statement))) {
                    $statements[] = trim($current_statement);
                }

                $executed_queries = 0;
                $failed_queries = 0;
                $failed_details = [];

                foreach ($statements as $statement) {
                    if (!empty($statement)) {
                        try {
                            $result = $pdo->exec($statement);
                            $executed_queries++;

                            // تسجيل تفاصيل الاستعلام المنفذ
                            if (stripos($statement, 'CREATE TABLE') !== false) {
                                preg_match('/CREATE TABLE.*?`?(\w+)`?\s*\(/i', $statement, $matches);
                                if (isset($matches[1])) {
                                    addStep('إنشاء جدول', 'success', "تم إنشاء جدول: {$matches[1]}");
                                }
                            }

                        } catch (PDOException $e) {
                            $failed_queries++;
                            $error_detail = "خطأ في الاستعلام: " . $e->getMessage();
                            $failed_details[] = $error_detail;

                            // تسجيل الخطأ مع تفاصيل أكثر
                            error_log("SQL Error: " . $e->getMessage() . "\nQuery: " . substr($statement, 0, 200));
                            addStep('خطأ SQL', 'error', substr($error_detail, 0, 100) . '...');
                        }
                    }
                }

                if ($failed_queries > 0) {
                    addError("فشل في تنفيذ $failed_queries استعلام من أصل " . count($statements));
                    addStep('إنشاء الجداول', 'error', "تم تنفيذ $executed_queries استعلام، فشل $failed_queries استعلام");

                    // عرض تفاصيل الأخطاء
                    foreach (array_slice($failed_details, 0, 3) as $detail) {
                        addError($detail);
                    }
                } else {
                    addStep('إنشاء الجداول', 'success', "تم تنفيذ جميع الاستعلامات بنجاح ($executed_queries استعلام)");
                }
            }

        } catch (PDOException $e) {
            addError('فشل في إنشاء الجداول: ' . $e->getMessage());
            addStep('إنشاء الجداول', 'error', $e->getMessage());
        } catch (Exception $e) {
            addError('خطأ في قراءة ملف SQL: ' . $e->getMessage());
            addStep('إنشاء الجداول', 'error', $e->getMessage());
        }
    }
    
    // الخطوة 6: التحقق من الجداول المنشأة
    addStep('التحقق من الجداول', 'progress', 'فحص الجداول المنشأة...');

    try {
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        $required_tables = ['imported_files', 'system_settings', 'activity_log', 'user_sessions'];
        $missing_tables = array_diff($required_tables, $tables);

        if (empty($missing_tables)) {
            addStep('التحقق من الجداول', 'success', 'جميع الجداول المطلوبة موجودة: ' . implode(', ', $tables));
        } else {
            addError('جداول مفقودة: ' . implode(', ', $missing_tables));
            addStep('التحقق من الجداول', 'error', 'جداول مفقودة: ' . implode(', ', $missing_tables));

            // محاولة إنشاء الجداول المفقودة يدوياً
            addStep('إنشاء الجداول المفقودة', 'progress', 'محاولة إنشاء الجداول المفقودة يدوياً...');

            $created_tables = [];
            $failed_tables = [];

            foreach ($missing_tables as $table) {
                try {
                    $sql = getTableSQL($table);
                    if ($sql) {
                        $pdo->exec($sql);
                        $created_tables[] = $table;
                        addStep("إنشاء جدول $table", 'success', "تم إنشاء الجدول بنجاح");
                    } else {
                        $failed_tables[] = $table;
                        addStep("إنشاء جدول $table", 'error', "لا يوجد SQL محدد لهذا الجدول");
                    }
                } catch (PDOException $e) {
                    $failed_tables[] = $table;
                    addError("فشل في إنشاء جدول $table: " . $e->getMessage());
                    addStep("إنشاء جدول $table", 'error', $e->getMessage());
                }
            }

            if (!empty($created_tables)) {
                addStep('إنشاء الجداول المفقودة', 'success', 'تم إنشاء: ' . implode(', ', $created_tables));
            }

            if (!empty($failed_tables)) {
                addStep('إنشاء الجداول المفقودة', 'error', 'فشل في إنشاء: ' . implode(', ', $failed_tables));
            }
        }

    } catch (PDOException $e) {
        addError('فشل في فحص الجداول: ' . $e->getMessage());
        addStep('التحقق من الجداول', 'error', $e->getMessage());
    }

    // الخطوة 6.5: إدراج الإعدادات الافتراضية
    addStep('إدراج الإعدادات الافتراضية', 'progress', 'إضافة الإعدادات الأساسية للنظام...');

    try {
        // التحقق من وجود جدول system_settings
        $tables = $pdo->query("SHOW TABLES LIKE 'system_settings'")->fetchAll();

        if (!empty($tables)) {
            $inserted_settings = insertDefaultSettings($pdo);
            addStep('إدراج الإعدادات الافتراضية', 'success', "تم إدراج/تحديث $inserted_settings إعداد");
        } else {
            addError('جدول system_settings غير موجود، لا يمكن إدراج الإعدادات');
            addStep('إدراج الإعدادات الافتراضية', 'error', 'جدول system_settings غير موجود');
        }

    } catch (PDOException $e) {
        addError('فشل في إدراج الإعدادات الافتراضية: ' . $e->getMessage());
        addStep('إدراج الإعدادات الافتراضية', 'error', $e->getMessage());
    }
    
    // الخطوة 7: تحديث ملف إعدادات قاعدة البيانات
    addStep('تحديث ملف الإعدادات', 'progress', 'تحديث config/database.php...');

    try {
        $config_file = 'config/database.php';
        if (file_exists($config_file)) {
            // إنشاء نسخة احتياطية
            $backup_file = $config_file . '.backup.' . date('Y-m-d-H-i-s');
            copy($config_file, $backup_file);

            $config_content = file_get_contents($config_file);

            if ($config_content === false) {
                addError('فشل في قراءة ملف الإعدادات');
                addStep('تحديث ملف الإعدادات', 'error', 'فشل في القراءة');
            } else {
                // تحديث الإعدادات مع حماية من الأخطاء
                $original_content = $config_content;

                $config_content = preg_replace(
                    "/define\('DB_HOST',\s*'[^']*'\);/",
                    "define('DB_HOST', '{$config['db_host']}');",
                    $config_content
                );
                $config_content = preg_replace(
                    "/define\('DB_NAME',\s*'[^']*'\);/",
                    "define('DB_NAME', '{$config['db_name']}');",
                    $config_content
                );
                $config_content = preg_replace(
                    "/define\('DB_USER',\s*'[^']*'\);/",
                    "define('DB_USER', '{$config['db_user']}');",
                    $config_content
                );
                $config_content = preg_replace(
                    "/define\('DB_PASS',\s*'[^']*'\);/",
                    "define('DB_PASS', '{$config['db_pass']}');",
                    $config_content
                );

                // التحقق من نجاح التحديث
                if ($config_content === $original_content) {
                    addStep('تحديث ملف الإعدادات', 'success', 'الإعدادات محدثة مسبقاً أو لم تتغير');
                } else {
                    if (file_put_contents($config_file, $config_content)) {
                        addStep('تحديث ملف الإعدادات', 'success', 'تم تحديث ملف الإعدادات بنجاح (نسخة احتياطية: ' . basename($backup_file) . ')');
                    } else {
                        addError('فشل في كتابة ملف الإعدادات');
                        addStep('تحديث ملف الإعدادات', 'error', 'فشل في الكتابة');
                        // استعادة النسخة الأصلية
                        copy($backup_file, $config_file);
                    }
                }
            }
        } else {
            addError('ملف الإعدادات غير موجود: ' . $config_file);
            addStep('تحديث ملف الإعدادات', 'error', 'الملف غير موجود');
        }

    } catch (Exception $e) {
        addError('خطأ في تحديث ملف الإعدادات: ' . $e->getMessage());
        addStep('تحديث ملف الإعدادات', 'error', $e->getMessage());
    }
    
    // الخطوة 8: اختبار النظام
    addStep('اختبار النظام', 'progress', 'اختبار الاتصال والوظائف الأساسية...');

    try {
        // اختبار الاتصال بقاعدة البيانات مع الإعدادات الجديدة
        $test_dsn = "mysql:host={$config['db_host']};dbname={$config['db_name']};charset={$config['db_charset']}";
        $test_pdo = new PDO($test_dsn, $config['db_user'], $config['db_pass'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);

        // اختبار استعلام بسيط
        $test_query = $test_pdo->query("SELECT COUNT(*) as count FROM system_settings")->fetch();

        if ($test_query && isset($test_query['count'])) {
            addStep('اختبار النظام', 'success', "النظام يعمل بشكل صحيح - تم العثور على {$test_query['count']} إعداد");
        } else {
            addError('فشل في اختبار النظام - لا توجد بيانات');
            addStep('اختبار النظام', 'error', 'فشل الاختبار - لا توجد بيانات');
        }

    } catch (Exception $e) {
        addError('خطأ في اختبار النظام: ' . $e->getMessage());
        addStep('اختبار النظام', 'error', $e->getMessage());
    }
    
    // إنهاء الإعداد
    if ($success && empty($errors)) {
        addStep('إنهاء الإعداد', 'success', 'تم إعداد المشروع بنجاح! النظام جاهز للاستخدام');

        // إضافة معلومات مفيدة
        addStep('معلومات مفيدة', 'info', 'الرابط الرئيسي: http://localhost/CDCO/');
        addStep('معلومات مفيدة', 'info', 'اختبار النظام: http://localhost/CDCO/test_system.php');
        addStep('معلومات مفيدة', 'info', 'ملف تجريبي: test.csv متوفر للاختبار');
    } else {
        addStep('إنهاء الإعداد', 'error', 'فشل في إعداد المشروع. يرجى مراجعة الأخطاء أعلاه');
        addStep('المساعدة', 'info', 'راجع ملف INSTALL.md للحصول على مساعدة مفصلة');
    }
    
} catch (Exception $e) {
    addError('خطأ عام: ' . $e->getMessage());
    addStep('خطأ عام', 'error', $e->getMessage());
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قاعدة البيانات - مدير ملفات CSV</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        .content {
            padding: 30px;
        }
        .config-section {
            background: #f8fafc;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid #e2e8f0;
        }
        .config-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .config-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        .config-label {
            font-weight: 500;
            color: #64748b;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }
        .config-value {
            font-family: monospace;
            background: #f1f5f9;
            padding: 8px;
            border-radius: 4px;
            font-size: 0.9rem;
            color: #1e293b;
        }
        .steps-container {
            background: #f8fafc;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .step {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 8px;
            border-right: 4px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        .step.success {
            border-right-color: #059669;
            background: #f0fdf4;
        }
        .step.error {
            border-right-color: #dc2626;
            background: #fef2f2;
        }
        .step.progress {
            border-right-color: #2563eb;
            background: #eff6ff;
        }
        .step.info {
            border-right-color: #64748b;
            background: #f8fafc;
        }
        .step-icon {
            font-size: 1.5rem;
            margin-left: 15px;
            width: 30px;
            text-align: center;
        }
        .step-icon.success { color: #059669; }
        .step-icon.error { color: #dc2626; }
        .step-icon.progress { color: #2563eb; }
        .step-icon.info { color: #64748b; }
        .step-content {
            flex: 1;
        }
        .step-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #1e293b;
        }
        .step-message {
            color: #64748b;
            font-size: 0.9rem;
        }
        .step-time {
            color: #94a3b8;
            font-size: 0.8rem;
            margin-right: 15px;
        }
        .summary {
            text-align: center;
            padding: 30px;
            border-top: 1px solid #e2e8f0;
        }
        .summary.success {
            background: linear-gradient(135deg, #059669, #047857);
            color: white;
        }
        .summary.error {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            color: white;
        }
        .summary-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        .summary h2 {
            margin: 0 0 15px 0;
            font-size: 1.8rem;
        }
        .summary p {
            margin: 0 0 25px 0;
            font-size: 1.1rem;
            opacity: 0.9;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: rgba(255,255,255,0.2);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 0 10px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255,255,255,0.3);
        }
        .btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        .errors-section {
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .error-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-right: 4px solid #dc2626;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .error-icon {
            color: #dc2626;
            font-size: 1.2rem;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #2563eb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .auto-refresh {
            text-align: center;
            padding: 15px;
            background: #eff6ff;
            border-radius: 8px;
            margin-top: 20px;
            color: #1e40af;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-database"></i> إعداد قاعدة البيانات</h1>
            <p>إعداد تلقائي لمشروع مدير ملفات CSV</p>
        </div>

        <div class="content">
            <!-- إعدادات قاعدة البيانات -->
            <div class="config-section">
                <div class="config-title">
                    <i class="fas fa-cog"></i>
                    إعدادات قاعدة البيانات المستخدمة
                </div>
                <div class="config-grid">
                    <div class="config-item">
                        <div class="config-label">خادم قاعدة البيانات</div>
                        <div class="config-value"><?php echo htmlspecialchars($config['db_host']); ?></div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">اسم قاعدة البيانات</div>
                        <div class="config-value"><?php echo htmlspecialchars($config['db_name']); ?></div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">اسم المستخدم</div>
                        <div class="config-value"><?php echo htmlspecialchars($config['db_user']); ?></div>
                    </div>
                    <div class="config-item">
                        <div class="config-label">ترميز قاعدة البيانات</div>
                        <div class="config-value"><?php echo htmlspecialchars($config['db_charset']); ?></div>
                    </div>
                </div>
            </div>

            <!-- الأخطاء إن وجدت -->
            <?php if (!empty($errors)): ?>
            <div class="errors-section">
                <div class="config-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    الأخطاء التي حدثت أثناء الإعداد
                </div>
                <?php foreach ($errors as $error): ?>
                <div class="error-item">
                    <i class="fas fa-times-circle error-icon"></i>
                    <span><?php echo htmlspecialchars($error); ?></span>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>

            <!-- خطوات الإعداد -->
            <div class="steps-container">
                <div class="config-title">
                    <i class="fas fa-list-ol"></i>
                    خطوات الإعداد
                </div>
                <?php foreach ($setup_steps as $step): ?>
                <div class="step <?php echo $step['status']; ?>">
                    <div class="step-icon <?php echo $step['status']; ?>">
                        <?php
                        switch ($step['status']) {
                            case 'success':
                                echo '<i class="fas fa-check-circle"></i>';
                                break;
                            case 'error':
                                echo '<i class="fas fa-times-circle"></i>';
                                break;
                            case 'progress':
                                echo '<div class="loading"></div>';
                                break;
                            default:
                                echo '<i class="fas fa-info-circle"></i>';
                        }
                        ?>
                    </div>
                    <div class="step-content">
                        <div class="step-title"><?php echo htmlspecialchars($step['step']); ?></div>
                        <?php if (!empty($step['message'])): ?>
                        <div class="step-message"><?php echo htmlspecialchars($step['message']); ?></div>
                        <?php endif; ?>
                    </div>
                    <div class="step-time"><?php echo $step['time']; ?></div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- ملخص النتيجة -->
        <div class="summary <?php echo $success && empty($errors) ? 'success' : 'error'; ?>">
            <div class="summary-icon">
                <?php if ($success && empty($errors)): ?>
                <i class="fas fa-check-circle"></i>
                <?php else: ?>
                <i class="fas fa-exclamation-triangle"></i>
                <?php endif; ?>
            </div>
            
            <?php if ($success && empty($errors)): ?>
            <h2>تم الإعداد بنجاح!</h2>
            <p>تم إعداد قاعدة البيانات والمشروع بالكامل. النظام جاهز للاستخدام الآن.</p>
            <a href="index.php" class="btn">
                <i class="fas fa-home"></i> الانتقال للصفحة الرئيسية
            </a>
            <a href="test_system.php" class="btn">
                <i class="fas fa-check-double"></i> اختبار النظام
            </a>
            <?php else: ?>
            <h2>فشل في الإعداد</h2>
            <p>حدثت أخطاء أثناء إعداد المشروع. يرجى مراجعة الأخطاء أعلاه وإصلاحها.</p>
            <a href="?retry=1" class="btn">
                <i class="fas fa-redo"></i> إعادة المحاولة
            </a>
            <a href="INSTALL.md" class="btn">
                <i class="fas fa-book"></i> دليل التثبيت
            </a>
            <?php endif; ?>
        </div>

        <?php if (!$success || !empty($errors)): ?>
        <div class="auto-refresh">
            <i class="fas fa-sync-alt"></i>
            سيتم إعادة تحميل الصفحة تلقائياً خلال 30 ثانية لإعادة المحاولة
        </div>
        <script>
            setTimeout(function() {
                if (confirm('هل تريد إعادة تشغيل عملية الإعداد؟')) {
                    location.reload();
                }
            }, 30000);
        </script>
        <?php endif; ?>
    </div>
</body>
</html>
