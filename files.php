<?php
/**
 * صفحة إدارة الملفات
 * Files Management Page
 */

require_once 'config/config.php';

// الحصول على قائمة الملفات
try {
    $db = getDB();
    $files = $db->fetchAll("
        SELECT id, original_name, table_name, total_rows, file_size, upload_date, status 
        FROM imported_files 
        ORDER BY upload_date DESC
    ");
} catch (Exception $e) {
    $files = [];
    $error = $e->getMessage();
}

$pageTitle = 'إدارة الملفات';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    <title><?php echo $pageTitle . ' - ' . SITE_TITLE; ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-file-csv"></i>
                    <?php echo SITE_TITLE; ?>
                </div>
                <nav class="nav">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                    <a href="files.php" class="nav-link active">
                        <i class="fas fa-folder"></i>
                        الملفات
                    </a>
                    <a href="search.php" class="nav-link">
                        <i class="fas fa-search"></i>
                        البحث
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="main-content">
        <div class="container">
            <!-- عنوان الصفحة -->
            <div class="card">
                <div class="card-header">
                    <h1 class="card-title">
                        <i class="fas fa-folder"></i>
                        إدارة الملفات المستوردة
                    </h1>
                    <p class="card-description">
                        عرض وإدارة جميع ملفات CSV المستوردة في النظام.
                    </p>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                    <div>
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            رفع ملف جديد
                        </a>
                    </div>
                    <div>
                        <button id="refreshFiles" class="btn btn-outline">
                            <i class="fas fa-sync-alt"></i>
                            تحديث
                        </button>
                    </div>
                </div>
            </div>

            <?php if (isset($error)): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                خطأ في تحميل الملفات: <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>

            <?php if (empty($files)): ?>
            <!-- رسالة عدم وجود ملفات -->
            <div class="card">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>لا توجد ملفات مستوردة</strong><br>
                        <a href="index.php">ارفع ملف CSV الأول</a> لتبدأ في استخدام النظام.
                    </div>
                </div>
            </div>
            <?php else: ?>

            <!-- قائمة الملفات -->
            <div class="card">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم الملف</th>
                                <th>الحالة</th>
                                <th>عدد السجلات</th>
                                <th>الحجم</th>
                                <th>تاريخ الرفع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($files as $file): ?>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        <i class="fas fa-file-csv text-primary"></i>
                                        <div>
                                            <strong><?php echo htmlspecialchars($file['original_name']); ?></strong>
                                            <br>
                                            <small class="text-secondary"><?php echo htmlspecialchars($file['table_name']); ?></small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $statusClass = '';
                                    $statusText = '';
                                    switch ($file['status']) {
                                        case 'completed':
                                            $statusClass = 'text-success';
                                            $statusText = 'مكتمل';
                                            break;
                                        case 'processing':
                                            $statusClass = 'text-warning';
                                            $statusText = 'قيد المعالجة';
                                            break;
                                        case 'error':
                                            $statusClass = 'text-danger';
                                            $statusText = 'خطأ';
                                            break;
                                    }
                                    ?>
                                    <span class="<?php echo $statusClass; ?>">
                                        <i class="fas fa-circle" style="font-size: 0.5rem;"></i>
                                        <?php echo $statusText; ?>
                                    </span>
                                </td>
                                <td><?php echo number_format($file['total_rows']); ?></td>
                                <td><?php echo formatFileSize($file['file_size']); ?></td>
                                <td><?php echo date('Y-m-d H:i', strtotime($file['upload_date'])); ?></td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                        <?php if ($file['status'] === 'completed'): ?>
                                        <a href="view.php?table=<?php echo urlencode($file['table_name']); ?>" 
                                           class="btn btn-outline btn-sm" title="عرض البيانات">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button onclick="exportData('<?php echo htmlspecialchars($file['table_name']); ?>', 'csv')"
                                                class="btn btn-outline btn-sm" title="تصدير CSV">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        <a href="search.php?table=<?php echo urlencode($file['table_name']); ?>"
                                           class="btn btn-outline btn-sm" title="بحث في البيانات">
                                            <i class="fas fa-search"></i>
                                        </a>
                                        <?php endif; ?>
                                        <button onclick="deleteFile(<?php echo $file['id']; ?>, '<?php echo htmlspecialchars($file['table_name']); ?>')" 
                                                class="btn btn-danger btn-sm" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="grid grid-3">
                <div class="card">
                    <div class="text-center">
                        <div class="stat-number text-primary">
                            <?php echo count(array_filter($files, fn($f) => $f['status'] === 'completed')); ?>
                        </div>
                        <div class="stat-label">ملفات مكتملة</div>
                    </div>
                </div>
                <div class="card">
                    <div class="text-center">
                        <div class="stat-number text-warning">
                            <?php echo count(array_filter($files, fn($f) => $f['status'] === 'processing')); ?>
                        </div>
                        <div class="stat-label">قيد المعالجة</div>
                    </div>
                </div>
                <div class="card">
                    <div class="text-center">
                        <div class="stat-number text-danger">
                            <?php echo count(array_filter($files, fn($f) => $f['status'] === 'error')); ?>
                        </div>
                        <div class="stat-label">أخطاء</div>
                    </div>
                </div>
            </div>

            <?php endif; ?>
        </div>
    </main>

    <!-- الفوتر -->
    <footer class="footer">
        <div class="container">
            <p>&copy; <?php echo date('Y'); ?> <?php echo SITE_TITLE; ?>. جميع الحقوق محفوظة.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        // تحديث قائمة الملفات
        document.getElementById('refreshFiles').addEventListener('click', function() {
            location.reload();
        });

        // تصدير البيانات
        async function exportData(tableName, format) {
            try {
                const response = await fetch(`api/export.php?table=${encodeURIComponent(tableName)}&format=${format}`);
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${tableName}.${format}`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    
                    showAlert('تم تصدير البيانات بنجاح', 'success');
                } else {
                    const result = await response.json();
                    showAlert(result.error || 'حدث خطأ أثناء التصدير', 'error');
                }
            } catch (error) {
                console.error('Export error:', error);
                showAlert('حدث خطأ أثناء التصدير', 'error');
            }
        }

        // حذف ملف
        async function deleteFile(fileId, tableName) {
            if (!confirm(`هل أنت متأكد من حذف الملف "${tableName}"؟\n\nسيتم حذف جميع البيانات المرتبطة بهذا الملف ولا يمكن التراجع عن هذا الإجراء.`)) {
                return;
            }

            try {
                const response = await fetch('api/delete.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_id: fileId,
                        table_name: tableName,
                        csrf_token: document.querySelector('meta[name="csrf-token"]').content
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('تم حذف الملف بنجاح', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showAlert(result.error, 'error');
                }

            } catch (error) {
                console.error('Delete error:', error);
                showAlert('حدث خطأ أثناء حذف الملف', 'error');
            }
        }
    </script>
</body>
</html>
