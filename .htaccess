# إعدادات Apache للأمان والأداء
# Apache Security and Performance Settings

# منع الوصول للملفات الحساسة
# Deny access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.log$">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "\.sql$">
    Order allow,deny
    Deny from all
</Files>

<Files ~ "config\.php$">
    Order allow,deny
    Deny from all
</Files>

# منع عرض محتوى المجلدات
# Disable directory browsing
Options -Indexes

# تفعيل ضغط الملفات
# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تحسين التخزين المؤقت
# Browser caching
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/ico "access plus 1 month"
    ExpiresByType image/icon "access plus 1 month"
    ExpiresByType text/plain "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
</IfModule>

# إعدادات الأمان
# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdnjs.cloudflare.com; font-src 'self' cdnjs.cloudflare.com; img-src 'self' data:;"
</IfModule>

# إعادة توجيه الأخطاء
# Error handling
ErrorDocument 404 /CDCO/404.html
ErrorDocument 403 /CDCO/403.html
ErrorDocument 500 /CDCO/500.html

# تحديد حجم الملفات المرفوعة
# Upload limits
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_execution_time 300
php_value memory_limit 256M

# تفعيل إعادة الكتابة
# Enable URL rewriting
RewriteEngine On

# إعادة توجيه HTTP إلى HTTPS (اختياري للإنتاج)
# Redirect HTTP to HTTPS (optional for production)
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# منع الوصول المباشر للملفات الحساسة
# Block direct access to sensitive files
RewriteRule ^config/ - [F,L]
RewriteRule ^logs/ - [F,L]
RewriteRule ^database/ - [F,L]

# إعادة توجيه للصفحة الرئيسية إذا لم يتم تحديد ملف
# Redirect to index if no file specified
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/CDCO/
RewriteRule ^$ /CDCO/index.php [L]
